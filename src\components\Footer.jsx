import React from 'react';
import {
  FaStepForward,
  FaFor<PERSON>,
  FaStepBackward,
  FaBackward,
} from "react-icons/fa";

// Import useTranslation if you plan to use it for pagination text like "Showing X of Y entries"
// import { useTranslation } from "react-i18next";

function Footer({
  currentPage,
  setCurrentPage,
  totalPages,
  filteredDataLength,
  itemsPerPage // ต้องรับ prop นี้มาด้วย
}) {
  // const { t } = useTranslation(); // Uncomment this line if you use t() for translation

  // ตรวจสอบว่าปุ่มก่อนหน้า/ถัดไปควรถูกปิดใช้งานหรือไม่
  const isPrevDisabled = currentPage === 0;
  // ปุ่มถัดไปจะถูกปิดใช้งานถ้าอยู่หน้าสุดท้าย (totalPages - 1) หรือถ้าไม่มีหน้าเลย (totalPages เป็น 0)
  const isNextDisabled = currentPage === totalPages - 1 || totalPages === 0;

  const handleNextPage = () => {
    if (!isNextDisabled) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (!isPrevDisabled) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleFirstPage = () => {
    setCurrentPage(0);
  };

  const handleLastPage = () => {
    // ตรวจสอบให้แน่ใจว่ามีหน้าอย่างน้อยหนึ่งหน้าก่อนที่จะพยายามไปหน้าสุดท้าย
    if (totalPages > 0) {
      setCurrentPage(totalPages - 1);
    }
  };

  // กำหนดช่วงของหมายเลขหน้าที่ต้องการแสดงในแถบ pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5; // เช่น แสดงหมายเลข 5 หน้าพร้อมกัน

    if (totalPages === 0) return []; // ไม่แสดงหน้าถ้า totalPages เป็น 0

    let startPage = Math.max(0, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages - 1, startPage + maxPagesToShow - 1);

    // ปรับ startPage ถ้าถึงหน้าสุดท้าย
    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(0, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  };

  const pageNumbersToDisplay = getPageNumbers();

  return (
    // คงรูปแบบและสีเดิมจากโค้ดที่คุณให้มา
    <div className="sticky bottom-0 left-0 right-0 bg-white text-white p-4 flex justify-center z-50 ml-0">
      <div className="flex flex-wrap justify-center items-center gap-2 sm:space-x-4">
        <button
          onClick={handleFirstPage}
          disabled={isPrevDisabled}
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaStepBackward />
        </button>
        <button
          onClick={handlePreviousPage}
          disabled={isPrevDisabled}
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaBackward />
        </button>
        <div className="flex flex-wrap justify-center gap-1 sm:space-x-2">
          {pageNumbersToDisplay.map((pageNum) => (
            <button
              key={pageNum}
              onClick={() => setCurrentPage(pageNum)}
              className={`px-3 py-1 sm:px-4 sm:py-2 rounded text-xs sm:text-sm ${
                pageNum === currentPage
                  ? "bg-gray-300 text-black border border-gray-600" // สีของหน้าที่ Active
                  : "bg-white text-black border border-gray-600" // สีของหน้าอื่น ๆ
              }`}
            >
              {pageNum + 1}
            </button>
          ))}
        </div>
        <button
          onClick={handleNextPage}
          disabled={isNextDisabled}
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaForward />
        </button>
        <button
          onClick={handleLastPage}
          disabled={isNextDisabled} // ใช้ isNextDisabled เดียวกันสำหรับปุ่ม "Last Page"
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaStepForward />
        </button>
      </div>
    </div>
  );
}

export default Footer;