import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";
// React-icons
import { FaSearch } from "react-icons/fa";

function CustomerMasterPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [searchTerm, setSearchTerm] = useState("");
  const [searchField, setSearchField] = useState("order_no");
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const isDeleting = useRef(false);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const res = await axios.get("/api/fetch/customer");
        setCustomers(res.data);
        setFilteredCustomers(res.data);
      } catch (error) {
        console.error("Failed to fetch customers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  // Add pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 9;

  // Calculate total pages
  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);

  // Get current page data
  const paginatedCustomers = filteredCustomers.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateClick = () => {
    navigate("/create-customer");
  };

  const handleEditClick = (order_no) => {
    const customer = customers.find((c) => c.order_no === order_no);
    if (!customer) {
      Swal.fire("Not found", "Customer not found.", "warning");
      return;
    }
    navigate(`/edit-customer/${order_no}`, {
      state: { customerToEdit: customer },
    });
  };

  const handleDeleteClick = async (id) => {
    const confirm = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this item?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirm.isConfirmed) {
      try {
        const response = await axios.delete("/api/delete/customer", {
          data: { id },
        });

        if (response.status === 200) {
          isDeleting.current = true;

          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Your data has been deleted.",
            showConfirmButton: false,
            timer: 1000,
          });

          // อัปเดต state ลบข้อมูลลูกค้าทิ้ง
          setCustomers((prev) => prev.filter((item) => item.id !== id));
          setFilteredCustomers((prev) => prev.filter((item) => item.id !== id));
        }
      } catch (error) {
        console.error("Error deleting customer:", error);
        Swal.fire("Error", "Failed to delete item.", "error");
      }
    }
  };

  useEffect(() => {
    const filtered = customers.filter((item) =>
      item[searchField]?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredCustomers(filtered);

    // ถ้าไม่ใช่ตอนลบ → รีเซ็ตไปหน้าแรก
    if (!isDeleting.current) {
      setCurrentPage(0);
    } else {
      // reset flag ทิ้ง
      isDeleting.current = false;
    }
  }, [searchTerm, searchField, customers]);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <h1
          data-testid="text-customer"
          name="textCustomer"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("customer.customer")}
        </h1>

        {/* Create Button */}
        <button
          data-testid="button-createForm-customer"
          name="buttonCreateFormCustomer"
          onClick={handleCreateClick}
          className="btn-create"
        >
          {t("action.create")}
        </button>
      </div>

      {/* Header Section */}
      <div className="flex flex-row items-center mb-3 gap-2">
        {/* Input Search */}
        <div className="relative w-full md:w-[300px] lg:w-[600px]">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            data-testid="search-customer"
            name="searchCustomer"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={t("customer.placeholder")}
            className="w-full pl-10 pr-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Select Field */}
        <select
          data-testid="select-customerType-search"
          name="selectCustomerTypeSearch"
          value={searchField}
          onChange={(e) => setSearchField(e.target.value)}
          className="w-[200px] px-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="order_no">{t("customer.orderNo")}</option>
          <option value="customer_abbreviation">
            {t("customer.customerAbbr")}
          </option>
          <option value="site_name">{t("customer.siteName")}</option>
          <option value="contact_person">{t("customer.person")}</option>
        </select>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-customer"
          name="tableCustomer"
          className="w-full bg-white border-collapse whitespace-nowrap"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("customer.orderNo")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[200px]">
                {t("customer.customerAbbr")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[200px]">
                {t("customer.siteName")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[150px]">
                {t("customer.person")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[150px]">
                {t("customer.email")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[150px]">
                {t("customer.rank")}
              </th>
              <th className="py-2 px-4 text-center border border-white min-w-[120px]">
                {t("userAccount.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              [...Array(9)].map((_, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  {[...Array(7)].map((_, cellIdx) => (
                    <td key={cellIdx} className="py-2 px-4 border border-white">
                      <div className="h-4 bg-[#E9EDF9] rounded animate-pulse w-full"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : paginatedCustomers.length > 0 ? (
              paginatedCustomers.map((customer, index) => (
                <tr
                  key={customer.order_no}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="py-1.5 px-4 border border-white">
                    {customer.order_no}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {customer.customer_abbreviation}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {customer.site_name}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {customer.contact_person}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {customer.contact_email}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {customer.rank}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    <div className="flex justify-center gap-4">
                      <EditButton
                        dataTestId="button-editForm-customer"
                        onClick={() => handleEditClick(customer.order_no)}
                      />
                      <DeleteButton
                        dataTestId="button-delete-customer"
                        onClick={() => handleDeleteClick(customer.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="7" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredCustomers.length}
      />
    </div>
  );
}

export default CustomerMasterPage;
