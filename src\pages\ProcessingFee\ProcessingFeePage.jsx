import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";

function ProcessingFeePage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [loading, setLoading] = useState(true);
  const [processingData, setProcessingData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);

  useEffect(() => {
    const fetchProcessing = async () => {
      try {
        const res = await axios.get("/api/fetch/processing");
        setProcessingData(res.data);
      } catch (error) {
        console.error("Error fetching processing data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProcessing();
  }, []);

  const itemsPerPage = 10;
  const totalPages = Math.ceil(processingData.length / itemsPerPage);
  const paginatedProcessing = processingData.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateProcessing = () => {
    navigate("/create-processing-fee");
  };

  const handleEditProcessing = (id) => {
    const processingFeeToEdit = processingData.find((item) => item.id === id);

    if (!processingFeeToEdit) {
      Swal.fire("Not found", "Processing fee not found.", "warning");
      return;
    }

    navigate(`/edit-processing-fee/${id}`, { state: { processingFeeToEdit } });
  };

  const handleDeleteClick = async (id) => {
    const confirm = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this item?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirm.isConfirmed) {
      try {
        const response = await axios.delete("/api/delete/processing", {
          data: { id }, // ส่ง payload ใน field `data`
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Your data has been deleted.",
            showConfirmButton: false,
            timer: 1000,
          });

          // ลบข้อมูลออกจาก state
          setProcessingData((prev) => prev.filter((item) => item.id !== id));
        }
      } catch (error) {
        console.error("Error deleting processing fee:", error);
        Swal.fire("Error", "Failed to delete item.", "error");
      }
    }
  };

  return (
    <div className="w-full">
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-processingFee"
          name="textProcessingFee"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("processingFee.processingFee")}
        </h1>
        <button
          data-testid="button-createForm-processingFee"
          name="createProcessingFee"
          type="button"
          onClick={handleCreateProcessing}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      <div className="overflow-x-auto">
        <table
          data-testid="table-processingFee"
          name="tableProcessingFee"
          className="w-full bg-white border-collapse"
        >
          <thead className="bg-[#4472C4] text-white text-base">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.processingItems")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.calculationCriteria")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.lowestPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.priceByMaterial")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.pattern")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-center min-w-[120px]">
                {t("processingFee.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              [...Array(itemsPerPage)].map((_, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  {Array(6)
                    .fill(0)
                    .map((_, i) => (
                      <td key={i} className="px-4 py-2">
                        <div className="h-4 bg-[#E9EDF9] rounded animate-pulse w-full"></div>
                      </td>
                    ))}
                </tr>
              ))
            ) : paginatedProcessing.length > 0 ? (
              paginatedProcessing.map((item, index) => (
                <tr
                  key={item.id}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="border border-white px-4 py-1">
                    {item.processing_items || ""}
                  </td>
                  <td className="border border-white px-4 py-1">
                    {item.calculation_criteria || ""}
                  </td>
                  <td className="border border-white px-4 py-1">
                    {item.lowest_price || ""}
                  </td>
                  <td className="border border-white px-4 py-1">
                    {item.price_by_material || ""}
                  </td>
                  <td className="border border-white px-4 py-1">
                    {item.pattern || ""}
                  </td>
                  <td className="border border-white px-4 py-1">
                    <div className="flex justify-center gap-2">
                      <EditButton
                        dataTestId="button-editForm-processingFee"
                        onClick={() => handleEditProcessing(item.id)}
                      />
                      <DeleteButton
                        dataTestId="button-delete-processingFee"
                        onClick={() => handleDeleteClick(item.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={processingData.length}
      />
    </div>
  );
}

export default ProcessingFeePage;
