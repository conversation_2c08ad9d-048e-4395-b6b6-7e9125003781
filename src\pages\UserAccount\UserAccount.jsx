import { useEffect, useState } from "react";
import Editbutton from "../../components/EditButton";
import Deletebutton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Swal from 'sweetalert2';

function UserAccount() {
  const [userAccounts, setUserAccounts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [searchInput, setSearchInput] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();
  const { t } = useTranslation();
  const itemsPerPage = 10;

  const handleCreate = () => {
    navigate("/usermanagement");
  };

  const fetchUserAccounts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/fetch/useraccount");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setUserAccounts(data);
    } catch (err) {
      console.error("Error fetching user accounts:", err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserAccounts();
  }, []);

  useEffect(() => {
    if (searchInput.trim() === "") {
      setSearchTerm("");
      setCurrentPage(0);
    }
  }, [searchInput]);

  const handleSearchClick = () => {
    setSearchTerm(searchInput);
    setCurrentPage(0);
  };

  // UPDATED: Pass the entire item (user data) in the state
  const handleEdit = (item) => {
    navigate(`/useredit`, { state: { userData: item } });
  };

  const handleDelete = async (id) => {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await fetch(`/api/delete/useraccount`, {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ id: id }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
          }

          Swal.fire(
            'Deleted!',
            'Your user account has been deleted.',
            'success'
          );
          fetchUserAccounts();
        } catch (err) {
          console.error("Error deleting user account:", err);
          Swal.fire(
            'Error!',
            `Failed to delete user account: ${err.message}`,
            'error'
          );
        }
      }
    });
  };

  const filteredData = userAccounts.filter(
    (item) =>
      item.user_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const paginatedData = filteredData.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1
          data-testid="text-useraccount"
          name="textUseraccount"
          className="text-2xl font-bold sm:mb-0 lg:ml-20 md:lg:ml-20 sm:ml-20"
        >
          {t("userAccount.userAccountList")}
        </h1>
        <button
          data-testid="button-createForm-user"
          name="buttonCreateFormuser"
          className="btn-create"
          onClick={handleCreate}
        >
          {t("action.create")}
        </button>
      </div>

      <div className="mt-1 w-full flex gap-4">
        <input
          data-testid="input-user-search"
          name="inputUsersearch"
          type="text"
          placeholder={t("userAccount.searchPlace")}
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="w-6/12 p-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />

        <button
          data-testid="button-user-search"
          name="buttonUsersearch"
          onClick={handleSearchClick}
          className="btn-search"
        >
          {t("userAccount.search")}
        </button>
      </div>

      <div className="mt-2 w-full overflow-x-auto">
        <table
          data-testid="table-user"
          name="table-user"
          className="w-full bg-white border-collapse"
        >
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[50px]">
                {t("userAccount.userID")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[200px]">
                {t("userAccount.username")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[200px]">
                {t("userAccount.password")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[100px]">
                {t("userAccount.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan="4" className="text-center py-4 text-gray-500">
                  loadingData...
                </td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan="4" className="text-center py-4 text-red-600">
                  errorLoadingData: {error}
                </td>
              </tr>
            ) : paginatedData.length === 0 ? (
              <tr>
                <td colSpan="4" className="text-center py-4 text-gray-500">
                  noDataFound
                </td>
              </tr>
            ) : (
              paginatedData.map((item, index) => (
                <tr
                  key={item.id || index}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="border border-white px-4 py-1">
                    {item.user_id}
                  </td>
                  <td className="border border-white px-4 py-1">
                    {item.username}
                  </td>
                  <td className="border border-white px-4 py-1">
                    ********
                  </td>
                  <td className="border border-white px-4 py-1">
                    <div className="flex justify-center space-x-2">
                      <Editbutton
                        dataTestId={`button-editForm-user-${item.id}`}
                        name={`buttonEditFormuser-${item.id}`}
                        onClick={() => handleEdit(item)} 
                      />
                      <Deletebutton
                        dataTestId={`button-user-delete-${item.id}`}
                        name={`buttonUserdelete-${item.id}`}
                        onClick={() => handleDelete(item.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <Footer
        className="fixed table-footer-group"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredData.length}
      />
    </div>
  );
}

export default UserAccount;
