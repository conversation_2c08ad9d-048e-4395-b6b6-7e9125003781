import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";
import { useTranslation } from "react-i18next";
import Swal from "sweetalert2";

function UserEdit() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const userDataToEdit = location.state?.userData;

  // State for form fields
  const [username, setUsername] = useState(""); // This corresponds to the 'username' field in UserManagement
  const [name1, setName1] = useState(""); // For "Name" (previously Name (Username)) - This is redundant with username in UserManagement, but kept for direct mapping
  const [branch, setBranch] = useState("");
  const [userID, setUserID] = useState("");
  const [email, setEmail] = useState("");
  const [department, setDepartment] = useState("");
  const [post, setPost] = useState("");
  const [name2, setName2] = useState(""); // For "Name" (next to surname)
  const [surname, setSurname] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [role, setRole] = useState("");

  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  // Renamed to avoid conflict with function below, internal state management
  const [saveSuccessInternal, setSaveSuccessInternal] = useState(false);
  const [saveError, setSaveError] = useState(null);

  // Custom function to handle save success with SweetAlert and navigation
  const handleSaveSuccess = (value) => {
    setSaveSuccessInternal(value); // Update internal state
    if (value) {
      Swal.fire({
        icon: "success",
        title: "Save Successfully!",
        showConfirmButton: true,
        confirmButtonText: "OK",
      }).then(() => {
        navigate("/user-account");
      });
    }
  };

  useEffect(() => {
    if (userDataToEdit) {
      setUsername(userDataToEdit.username || ""); // Set username
      setName1(userDataToEdit.name1 || ""); // Set name1 (corresponds to name1 in UserManagement)
      setBranch(userDataToEdit.branch || "");
      setUserID(userDataToEdit.user_id || "");
      setEmail(userDataToEdit.email || "");
      setDepartment(userDataToEdit.department || "");
      setPost(userDataToEdit.post || "");
      setName2(userDataToEdit.name2 || "");
      setSurname(userDataToEdit.surname || "");
      setPassword(""); // Passwords are not pre-filled for security
      setConfirmPassword(""); // Passwords are not pre-filled for security
      setRole(userDataToEdit.role || "");
      setIsLoading(false);
      setFetchError(null);
    } else {
      setIsLoading(false);
      setFetchError(
        "No user data provided for editing. Please select a user from the list."
      );
      // Clear fields if no data is provided
      setUsername("");
      setName1("");
      setBranch("");
      setUserID("");
      setEmail("");
      setDepartment("");
      setPost("");
      setName2("");
      setSurname("");
      setPassword("");
      setConfirmPassword("");
      setRole("");
    }
  }, [userDataToEdit, t]);

  const handleCancel = () => {
    navigate("/user-account");
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);
    handleSaveSuccess(false); // Reset success state

    const id = userDataToEdit?.id;

    if (!id) {
      const errorMessage = "Cannot save: User ID is missing from user data.";
      setSaveError(errorMessage);
      Swal.fire({
        icon: "error",
        title: "Save Error",
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    const userData = {
      id,
      username: username, // Send username to the backend
      name1: name1, // Send name1 to the backend
      branch,
      user_id: userID,
      email,
      department,
      post,
      name2,
      surname,
      ...(password && { password }), // Only include password if it's set
      role,
    };

    // --- Validation Logic ---
    const requiredFields = [
      { value: username, label: t("userAccount.username") },
      { value: name1, label: t("userAccount.name") }, // Updated label for name1
      { value: branch, label: t("userAccount.branch") },
      { value: userID, label: t("userAccount.userID") },
      { value: email, label: t("userAccount.email") },
      { value: department, label: t("userAccount.department") },
      { value: post, label: t("userAccount.post") },
      { value: name2, label: t("userAccount.name") }, // Generic "Name"
      { value: surname, label: t("userAccount.surName") },
      { value: role, label: t("userAccount.role") || "Role" },
    ];

    if (password || confirmPassword) {
      // If either password field is touched, both become required
      requiredFields.push({
        value: password,
        label: t("userAccount.password"),
      });
      requiredFields.push({
        value: confirmPassword,
        label: t("userAccount.confirmPassword"),
      });
    }

    const missingFields = [];
    requiredFields.forEach((field) => {
      if (typeof field.value === "string" && field.value.trim() === "") {
        missingFields.push(field.label);
      } else if (field.value === null || field.value === undefined) {
        missingFields.push(field.label);
      }
    });

    if (missingFields.length > 0) {
      const errorMessage = `missingFields: ${missingFields.join(", ")}.`;
      setSaveError(errorMessage);
      Swal.fire({
        icon: "error",
        title: t("validation.validationError"),
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    if (password && password !== confirmPassword) {
      // Check password match only if password is provided
      setSaveError("Passwords do not match.");
      Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Passwords do not match.",
      });
      setIsSaving(false);
      return;
    }
    // --- End Validation Logic ---

    try {
      const response = await fetch(`/api/edit/useraccount`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`
        );
      }

      handleSaveSuccess(true); // This will trigger the Swal.fire and navigate
    } catch (err) {
      const displayErrorMessage = err.message;
      setSaveError(displayErrorMessage);
      console.error("Error saving user:", err);
      Swal.fire({
        icon: "error",
        title: t("action.saveError"),
        text: displayErrorMessage,
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-10 text-gray-700">
        {t("material.loadingData")}
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="text-center py-10 text-red-600">
        {t("material.errorLoadingData")}: {fetchError}
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2
        data-testid="text-editFormUser-edit"
        name="texteditFormUseredit"
        className="text-2xl font-bold mb-6"
      >
        {t("userAccount.userEdit")}
      </h2>
      <form className="grid grid-cols-1 gap-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Row 1: Username & Name (previously Name (Username)) */}
          <div>
            <label
              data-testid="text-editFormUser-username"
              name="texteditFormUserusername"
              className="block font-semibold mb-1"
            >
              {t("userAccount.username")}
            </label>
            <input
              data-testid="input-editFormUser-username"
              name="inputeditFormUserusername"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-editFormUser-name1"
              name="texteditFormUsername1"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")}
            </label>
            <input
              data-testid="input-editFormUser-name1"
              name="inputeditFormUsername1"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={name1}
              onChange={(e) => setName1(e.target.value)}
            />
          </div>

          {/* Row 2: User ID & Email */}
          <div>
            <label
              data-testid="text-editFormUser-userid"
              name="texteditFormUseruserid"
              className="block font-semibold mb-1"
            >
              {t("userAccount.userID")}
            </label>
            <input
              data-testid="input-editFormUser-userid"
              name="input-editFormUser-userid"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={userID}
              onChange={(e) => setUserID(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-editFormUser-email"
              name="texteditFormUseremail"
              className="block font-semibold mb-1"
            >
              {t("userAccount.email")}
            </label>
            <input
              data-testid="input-editFormUser-email"
              name="inputeditFormUseremail"
              type="email"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          {/* Row 3: Branch & Department */}
          <div>
            <label
              data-testid="text-editFormUser-branch"
              name="texteditFormUserbranch"
              className="block font-semibold mb-1"
            >
              {t("userAccount.branch")}
            </label>
            <select
              data-testid="select-editFormUser-branch"
              name="selecteditFormUserbranch"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={branch}
              onChange={(e) => setBranch(e.target.value)}
            >
              <option value="">Select an option</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>
          <div>
            <label
              data-testid="text-editFormUser-department"
              name="texteditFormUserdepartment"
              className="block font-semibold mb-1"
            >
              {t("userAccount.department")}
            </label>
            <select
              data-testid="select-editFormUser-department"
              name="selecteditFormUserdepartment"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={department}
              onChange={(e) => setDepartment(e.target.value)}
            >
              <option value="">Select an option</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>

          {/* Row 4: Post & Role */}
          <div>
            <label
              data-testid="text-editFormUser-post"
              name="texteditFormUserpost"
              className="block font-semibold mb-1"
            >
              {t("userAccount.post")}
            </label>
            <select
              data-testid="select-editFormUser-post"
              name="sselecteditFormUserpost"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={post}
              onChange={(e) => setPost(e.target.value)}
            >
              <option value="">Select an option</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>
          <div>
            <label
              data-testid="text-editFormUser-role"
              name="texteditFormUserrole"
              className="block font-semibold mb-1"
            >
              Role
            </label>
            <select
              data-testid="select-editFormUser-role"
              name="selecteditFormUserrole"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={role}
              onChange={(e) => setRole(e.target.value)}
            >
              <option value="">Select a role</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>

          {/* Row 5: Name (name2) & Surname */}
          <div>
            <label
              data-testid="text-editFormUser-name2"
              name="texteditFormUsername2"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")} {/* Generic "Name" for name2 */}
            </label>
            <input
              data-testid="input-editFormUser-name2"
              name="inputeditFormUsername2"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={name2}
              onChange={(e) => setName2(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-editFormUser-surname"
              name="texteditFormUsersurname"
              className="block font-semibold mb-1"
            >
              {t("userAccount.surName")}
            </label>
            <input
              data-testid="input-editFormUser-surname"
              name="inputeditFormUsersurname"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={surname}
              onChange={(e) => setSurname(e.target.value)}
            />
          </div>

          {/* Row 6: Password & Confirm Password */}
          <div>
            <label
              data-testid="text-editFormUser-password"
              name="texteditFormUserpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.password")}
            </label>
            <input
              type="password"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              data-testid="iinput-editFormUser-password"
              name="inputeditFormUserpassword"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-editFormUser-confirmpassword"
              name="texteditFormUserconfirmpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.confirmPassword")}
            </label>
            <input
              data-testid="input-editFormUser-confirmpassword"
              name="inputeditFormUserconfirmpassword"
              type="password"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>
        </div>
      </form>

      {/* Status Messages */}
      {isSaving && (
        <div className="text-center py-2 text-blue-600">saving...</div>
      )}
      {saveError && (
        <div className="text-center py-2 text-red-600">{saveError}</div>
      )}
      {saveSuccessInternal && ( // Use internal state for display
        <div className="text-center py-2 text-green-600">SaveSuccess....</div>
      )}

      {/* Action Buttons */}
      <div className="mt-6 flex gap-4">
        <Savebutton
          dataTestId="button-editFormUser-saveuser"
          name="buttonEditFormUsersaveuser"
          onClick={handleSave}
          disabled={isSaving}
        />
        <Canclebutton
          dataTestId="button-editFormUser-canceluser"
          name="buttonEditFormUsercanceluser"
          onClick={handleCancel}
          disabled={isSaving}
        />
      </div>
    </div>
  );
}

export default UserEdit;
