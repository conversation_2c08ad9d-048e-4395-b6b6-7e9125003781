import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function EditProcessingFee() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const { processingFeeToEdit } = location.state || {};

  const [id, setId] = useState(null);
  const [formData, setFormData] = useState({
    processing_items: "",
    calculation_criteria: "",
    lowest_price: "",
    price_by_material: "",
    pattern: "",
  });

  useEffect(() => {
    if (processingFeeToEdit) {
      setFormData({
        processing_items: processingFeeToEdit.processing_items || "",
        calculation_criteria: processingFeeToEdit.calculation_criteria || "",
        lowest_price: processingFeeToEdit.lowest_price || "",
        price_by_material: processingFeeToEdit.price_by_material || "",
        pattern: processingFeeToEdit.pattern || "",
      });
      setId(processingFeeToEdit.id || null);
    }
  }, [processingFeeToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleUpdate = async () => {
    const {
      processing_items,
      calculation_criteria,
      lowest_price,
      price_by_material,
      pattern,
    } = formData;

    if (
      !processing_items.trim() ||
      !calculation_criteria.trim() ||
      !lowest_price.toString().trim() ||
      !price_by_material.toString().trim() ||
      !pattern.trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const payload = {
        id,
        processing_items,
        calculation_criteria,
        lowest_price,
        price_by_material,
        pattern,
      };

      const response = await axios.put("/api/edit/processing", payload);

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/processing-fee");
      }
    } catch (error) {
      console.error("Error updating data:", error);
      Swal.fire({
        icon: "error",
        title: "Error updating data",
        text: error.response?.data?.error || "Unable to update the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/processing-fee");
  };

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("processingFee.processingFeeEdit")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton onClick={handleUpdate} />
          <Canclebutton onClick={handleCancel} />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.processingItems")}
              </label>
              <input
                name="processing_items"
                value={formData.processing_items}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.calculationCriteria")}
              </label>
              <input
                name="calculation_criteria"
                value={formData.calculation_criteria}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.lowestPrice")}
              </label>
              <input
                name="lowest_price"
                value={formData.lowest_price}
                onChange={handleChange}
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.priceByMaterial")}
              </label>
              <input
                name="price_by_material"
                value={formData.price_by_material}
                onChange={handleChange}
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.pattern")}
              </label>
              <input
                name="pattern"
                value={formData.pattern}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>
        </div>
      </div>

      {/* ตารางแสดง preview */}
      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.processingItems")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.calculationCriteria")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.lowestPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.priceByMaterial")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.pattern")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-[#E9EDF9]">
              <td className="border border-gray-300 px-4 py-1">
                {formData.processing_items}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.calculation_criteria}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.lowest_price}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.price_by_material}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.pattern}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default EditProcessingFee;
