import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";
import { v4 as uuidv4 } from "uuid";

// import components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";
import Footer from "../../components/Footer";

function CreateBIS() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [bisData, setBisData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 7;
  const totalPages = Math.ceil(bisData.length / itemsPerPage);
  const paginatedData = bisData.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const [formData, setFormData] = useState({
    screw_type: "",
    color: "",
    price: "",
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get("/api/fetch/bis");
        setBisData(response.data);
      } catch (error) {
        console.error("Failed to fetch BIS data:", error);
      }
    };
    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const generateShortId = () => {
    return uuidv4().replace(/-/g, "").slice(0, 12);
  };

  const handleSave = async (event) => {
    event.preventDefault();

    const { screw_type, color, price } = formData;

    if (!screw_type.trim() || !color.trim() || !price.toString().trim()) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    const bis_no = generateShortId();

    const dataToSend = {
      ...formData,
      bis_no,
    };

    try {
      const response = await axios.post("/api/create/bis", dataToSend);

      if (response.status === 201 || response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Saved successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/bis");
      }
    } catch (error) {
      console.error("Error saving BIS data:", error);
      Swal.fire({
        icon: "error",
        title: "An error occurred while saving.",
        text: error.response?.data?.error || "Unable to save the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/bis");
  };

  return (
    <div>
      <form onSubmit={handleSave}>
        <div className="flex flex-wrap justify-between items-center w-full">
          <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
            {t("bis.bisAdd")}
          </h1>
          <div className="flex items-center gap-4 ml-auto mr-4">
            <Savebutton type="submit" />
            <Canclebutton onClick={handleCancel} />
          </div>
        </div>

        <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
          <div className="flex flex-col gap-4">
            {/* Row 1 */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("bis.screwType")}
                </label>
                <input
                  type="text"
                  name="screw_type"
                  value={formData.screw_type}
                  onChange={handleChange}
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">{t("bis.price")}</label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleChange}
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
            </div>

            {/* Row 2 */}
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">{t("bis.color")}</label>
              <input
                type="text"
                name="color"
                value={formData.color}
                onChange={handleChange}
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>
        </div>
      </form>

      {/* Table ส่วนล่าง */}
      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.screwType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.color")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.price")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.length > 0 ? (
              paginatedData.map((item, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="border border-gray-300 px-4 py-1">
                    {item.screw_type}
                  </td>
                  <td className="border border-gray-300 px-4 py-1">
                    {item.color}
                  </td>
                  <td className="border border-gray-300 px-4 py-1">
                    {item.price}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={3} className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={bisData.length}
      />
    </div>
  );
}

export default CreateBIS;
