import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import Components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";
import Footer from "../../components/Footer";

function CreateProcessingFee() {
  const [formData, setFormData] = useState({
    processing_items: "",
    calculation_criteria: "",
    lowest_price: "",
    price_by_material: "",
    pattern: "",
  });

  const navigate = useNavigate();
  const { t } = useTranslation();

  const [processingData, setProcessingData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await axios.get("/api/fetch/processing");
        setProcessingData(res.data);
      } catch (error) {
        console.error("Failed to fetch processing data:", error);
      }
    };
    fetchData();
  }, []);

  const itemsPerPage = 5;
  const totalPages = Math.ceil(processingData.length / itemsPerPage);
  const paginatedData = processingData.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async (event) => {
    event.preventDefault();

    const {
      processing_items,
      calculation_criteria,
      lowest_price,
      price_by_material,
      pattern,
    } = formData;

    // Check for empty required fields
    if (
      !processing_items.trim() ||
      !calculation_criteria.trim() ||
      !lowest_price.trim() ||
      !price_by_material.trim() ||
      !pattern.trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const response = await axios.post("/api/create/processing", formData);

      if (response.status === 201 || response.status === 200) {
        Swal.fire({
          icon: "success",
          title: "Saved successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/processing-fee");
      }
    } catch (error) {
      console.error("Error saving data:", error);
      Swal.fire({
        icon: "error",
        title: "An error occurred while saving.",
        text: error.response?.data?.error || "Unable to save the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/processing-fee");
  };

  return (
    <div>
      <form onSubmit={handleSave}>
        <div className="flex flex-wrap justify-between items-center w-full">
          <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
            {t("processingFee.processingFeeAdd")}
          </h1>
          <div className="flex items-center gap-4 ml-auto mr-4">
            <Savebutton type="submit" />
            <Canclebutton onClick={handleCancel} />
          </div>
        </div>

        <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
          <div className="flex flex-col gap-4">
            {/* Row 1 */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("processingFee.processingItems")}
                </label>
                <input
                  type="text"
                  name="processing_items"
                  value={formData.processing_items}
                  onChange={handleChange}
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("processingFee.calculationCriteria")}
                </label>
                <input
                  type="text"
                  name="calculation_criteria"
                  value={formData.calculation_criteria}
                  onChange={handleChange}
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
            </div>

            {/* Row 2 */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("processingFee.lowestPrice")}
                </label>
                <input
                  type="number"
                  name="lowest_price"
                  value={formData.lowest_price}
                  onChange={handleChange}
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("processingFee.priceByMaterial")}
                </label>
                <input
                  type="number"
                  name="price_by_material"
                  value={formData.price_by_material}
                  onChange={handleChange}
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
            </div>

            {/* Row 3 */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("processingFee.pattern")}
                </label>
                <input
                  type="text"
                  name="pattern"
                  value={formData.pattern}
                  onChange={handleChange}
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
            </div>
          </div>
        </div>
      </form>

      {/* Table ส่วนล่าง */}
      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.processingItems")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.calculationCriteria")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.lowestPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.priceByMaterial")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.pattern")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-gray-300 px-4 py-1">
                  {item.processing_items}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.calculation_criteria}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.lowest_price}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.price_by_material}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.pattern}
                </td>
              </tr>
            ))}
            {paginatedData.length === 0 && (
              <tr>
                <td colSpan="9" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={processingData.length}
      />
    </div>
  );
}

export default CreateProcessingFee;
