import Select from "react-select";

function CustomSelect({
  name,
  value,
  options,
  onChange,
  menuPlacement = "auto",
}) {
  const handleChange = (selectedOption) => {
    onChange({
      target: {
        name,
        value: selectedOption ? selectedOption.value : "",
      },
    });
  };

  const selected = options.find((opt) => opt.value === value) || null;

  return (
    <div>
      <Select
        name={name}
        value={selected}
        onChange={handleChange}
        options={options}
        isClearable
        menuPlacement={menuPlacement}
        placeholder=""
        className="react-select-container border border-gray-600 rounded-sm"
        classNamePrefix="react-select"
      />
    </div>
  );
}

export default CustomSelect;
