import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import Components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";
import CustomSelect from "../../components/CustomSelect";

function EditCustomerMaster() {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { customerToEdit } = location.state || {};

  const [id, setId] = useState(null);
  const [formData, setFormData] = useState({
    order_no: "",
    fixed_rate_adjustment: "",
    customer_abbreviation: "",
    site_name: "",
    recipient_contact: "",
    contact_person: "",
    contact_email: "",
    delivery_destination: "",
    shipping_class: "",
    hole_drilling_pattern: "",
    processing_pattern: "",
    hole_pattern: "",
    rank: "",
  });

  const [rankOptions, setRankOptions] = useState([]);

  const rankSelectOptions = rankOptions.map((rank) => ({
    value: rank.rank_name,
    label: rank.rank_name,
  }));

  const shippingClassOptions = [
    { value: "class1", label: "Class 1" },
    { value: "class2", label: "Class 2" },
    { value: "class3", label: "Class 3" },
  ];

  const holeDrillingPatternOptions = [
    { value: "cost1", label: "Cost 1" },
    { value: "cost2", label: "Cost 2" },
    { value: "cost3", label: "Cost 3" },
  ];

  const processingPatternOptions = [
    { value: "pattern1", label: "Pattern 1" },
    { value: "pattern2", label: "Pattern 2" },
    { value: "pattern3", label: "Pattern 3" },
  ];

  const holePatternOptions = [
    { value: "pattern1", label: "Pattern 1" },
    { value: "pattern2", label: "Pattern 2" },
    { value: "pattern3", label: "Pattern 3" },
  ];

  useEffect(() => {
    const fetchRanks = async () => {
      try {
        const response = await axios.get("/api/fetch/rank");
        if (response.data) {
          setRankOptions(response.data);
        }
      } catch (error) {
        console.error("Error fetching ranks:", error);
      }
    };

    fetchRanks();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleUpdate = async () => {
    const {
      order_no,
      fixed_rate_adjustment,
      customer_abbreviation,
      site_name,
      recipient_contact,
      contact_person,
      contact_email,
      delivery_destination,
      shipping_class,
      hole_drilling_pattern,
      processing_pattern,
      hole_pattern,
      rank,
    } = formData;

    // ตรวจสอบทุกฟิลด์ต้องไม่เว้นว่าง (trim แล้วไม่เป็น empty string)
    if (
      !order_no.toString().trim() ||
      !fixed_rate_adjustment.toString().trim() ||
      !customer_abbreviation.toString().trim() ||
      !site_name.toString().trim() ||
      !recipient_contact.toString().trim() ||
      !contact_person.toString().trim() ||
      !contact_email.toString().trim() ||
      !delivery_destination.toString().trim() ||
      !shipping_class.toString().trim() ||
      !hole_drilling_pattern.toString().trim() ||
      !processing_pattern.toString().trim() ||
      !hole_pattern.toString().trim() ||
      !rank.toString().trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const payload = {
        id,
        order_no,
        fixed_rate_adjustment,
        customer_abbreviation,
        site_name,
        recipient_contact,
        contact_person,
        contact_email,
        delivery_destination,
        shipping_class,
        hole_drilling_pattern,
        processing_pattern,
        hole_pattern,
        rank,
      };

      const response = await axios.put("/api/edit/customer", payload);

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/customer-master");
      }
    } catch (error) {
      console.error("Error updating data:", error);
      Swal.fire({
        icon: "error",
        title: "Error updating data",
        text: error.response?.data?.error || "Unable to update the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/customer-master");
  };

  useEffect(() => {
    if (customerToEdit) {
      setFormData({
        order_no: customerToEdit.order_no ?? "",
        fixed_rate_adjustment: customerToEdit.fixed_rate_adjustment ?? "",
        customer_abbreviation: customerToEdit.customer_abbreviation ?? "",
        site_name: customerToEdit.site_name ?? "",
        recipient_contact: customerToEdit.recipient_contact ?? "",
        contact_person: customerToEdit.contact_person ?? "",
        contact_email: customerToEdit.contact_email ?? "",
        delivery_destination: customerToEdit.delivery_destination ?? "",
        shipping_class: customerToEdit.shipping_class ?? "",
        hole_drilling_pattern: customerToEdit.hole_drilling_pattern ?? "",
        processing_pattern: customerToEdit.processing_pattern ?? "",
        hole_pattern: customerToEdit.hole_pattern ?? "",
        rank: customerToEdit.rank ?? "",
      });
      setId(customerToEdit.id);
    }
  }, [customerToEdit]);

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-customer-edit"
        className="text-lg sm:text-xl md:text-2xl font-bold mb-8"
      >
        {t("customer.customerEdit")}
      </h1>

      <div className="grid grid-cols-2 gap-x-6 gap-y-2">
        {/* First Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-orderNo"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.orderNo")}
          </label>
          <input
            data-testid="input-editFormCustomer-orderNo"
            type="text"
            name="order_no"
            value={formData.order_no}
            readOnly
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 bg-gray-100"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-fixedRate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.fixedRate")}
          </label>
          <input
            data-testid="input-editFormCustomer-fixedRate"
            type="number"
            name="fixed_rate_adjustment"
            value={formData.fixed_rate_adjustment}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Second Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-customerAbbr"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.customerAbbr")}
          </label>
          <input
            data-testid="input-editFormCustomer-customerAbbr"
            type="text"
            name="customer_abbreviation"
            value={formData.customer_abbreviation}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-siteName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.siteName")}
          </label>
          <input
            data-testid="input-editFormCustomer-siteName"
            type="text"
            name="site_name"
            value={formData.site_name}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Third Row */}
        <div>
          <label
            data-testid="text-createFormCustomer-contactPerson"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.person")}
          </label>
          <input
            data-testid="input-createFormCustomer-contactPerson"
            type="text"
            name="contact_person"
            value={formData.contact_person}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-email"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.email")}
          </label>
          <input
            data-testid="input-editFormCustomer-email"
            type="email"
            name="contact_email"
            value={formData.contact_email}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Fourth Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-deliveryDestination"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.deliveryDestination")}
          </label>
          <input
            data-testid="input-editFormCustomer-deliveryDestination"
            type="text"
            name="delivery_destination"
            value={formData.delivery_destination}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-shippingClass"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.shippingClass")}
          </label>
          <CustomSelect
            data-testid="select-createFormCustomer-shippingClass"
            name="shipping_class"
            value={formData.shipping_class}
            onChange={handleChange}
            options={shippingClassOptions}
          />
        </div>

        {/* Fifth Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-holeDrillingPattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.holeDrillingPattern")}
          </label>
          <CustomSelect
            data-testid="select-createFormCustomer-holeDrillingPattern"
            name="hole_drilling_pattern"
            value={formData.hole_drilling_pattern}
            onChange={handleChange}
            options={holeDrillingPatternOptions}
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-processingPattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.processingPattern")}
          </label>
          <CustomSelect
            data-testid="select-createFormCustomer-processingPattern"
            name="processing_pattern"
            value={formData.processing_pattern}
            onChange={handleChange}
            options={processingPatternOptions}
          />
        </div>

        {/* Sixth Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-holePattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.holePattern")}
          </label>
          <CustomSelect
            data-testid="select-createFormCustomer-holePattern"
            name="hole_pattern"
            value={formData.hole_pattern}
            onChange={handleChange}
            options={holePatternOptions}
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-rank"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.rank")}
          </label>
          <CustomSelect
            data-testid="select-createFormCustomer-rank"
            name="rank"
            value={formData.rank}
            options={rankSelectOptions}
            onChange={handleChange}
            menuPlacement="top"
          />
        </div>

        {/* Seventh Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-recipientContact"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.recipientContact")}
          </label>
          <input
            data-testid="input-editFormCustomer-recipientContact"
            type="text"
            name="recipient_contact"
            value={formData.recipient_contact}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div></div>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 mt-8">
        <SaveButton
          data-testid="button-editFormCustomer-saveCustomer"
          onClick={handleUpdate}
        />
        <CancelButton
          data-testid="button-editFormCustomer-saveCustomer"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default EditCustomerMaster;
