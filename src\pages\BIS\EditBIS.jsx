import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// import components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function EditBIS() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { bisToEdit } = location.state || {};

  const [formData, setFormData] = useState({
    screw_type: "",
    color: "",
    price: "",
  });

  const [id, setId] = useState(null); 

  useEffect(() => {
    if (bisToEdit) {
      setFormData({
        screw_type: bisToEdit.screw_type || "",
        color: bisToEdit.color || "",
        price: bisToEdit.price || "",
      });
      setId(bisToEdit.id || null);
    }
  }, [bisToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleUpdate = async () => {
    const { screw_type, color, price } = formData;

    if (!screw_type.trim() || !color.trim() || !price.toString().trim()) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    const payload = {
      id,
      screw_type,
      color,
      price,
    };

    try {
      const response = await axios.put("/api/edit/bis", payload);

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/bis");
      }
    } catch (error) {
      console.error("Error updating BIS:", error);
      Swal.fire({
        icon: "error",
        title: "Error updating data",
        text: error.response?.data?.error || "Unable to update the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/bis");
  };

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("bis.bisAdd")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton onClick={handleUpdate} />
          <Canclebutton onClick={handleCancel} />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("bis.screwType")}
              </label>
              <input
                name="screw_type"
                type="text"
                value={formData.screw_type}
                onChange={handleChange}
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">{t("bis.price")}</label>
              <input
                name="price"
                type="number"
                value={formData.price}
                onChange={handleChange}
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="w-full md:w-96">
            <label className="block font-bold mb-1">{t("bis.color")}</label>
            <input
              name="color"
              type="text"
              value={formData.color}
              onChange={handleChange}
              className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
            />
          </div>
        </div>
      </div>

      {/* Preview Table */}
      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.screwType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.color")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.price")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-[#E9EDF9]">
              <td className="border border-gray-300 px-4 py-1">
                {formData.screw_type}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.color}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.price}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default EditBIS;
