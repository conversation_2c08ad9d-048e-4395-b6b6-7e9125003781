@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* Custom styles Sidebar */
.active-link {
  background-color: rgb(189, 224, 248);
  color: #0860ad;
}

.hover-link:hover {
  background-color: rgb(189, 224, 248);
  color: #0860ad;
}

/* CUSTOM BUTTONS STYLES */
@layer components {
  .btn-create {
    @apply bg-[#0a6dc2] text-white text-lg font-medium px-9 py-2 rounded-md shadow-md hover:bg-blue-800;
  }

  .btn-search {
    @apply bg-green-500 text-white text-lg font-medium px-9 py-2 rounded-md shadow-md hover:bg-green-600;
  }
}

/* Icons Clear Date In Datepicker for Projectlist */
.projectlist-page .react-datepicker__close-icon {
  width: 24px !important;
  height: 24px !important;
  padding: 4px !important;
  background-size: 12px 12px !important;
  top: -7px !important;
  right: 10px !important;
}

.projectlist-page .react-datepicker__close-icon::after {
  background-color: transparent;
  color: red;
  font-size: 28px;
  font-weight: bold;
}

/* Icons Clear Date In Datepicker for General page */
.react-datepicker__close-icon {
  width: 24px !important;
  height: 24px !important;
  padding: 4px !important;
  background-size: 12px 12px !important;
  top: -4px !important;
  right: 10px !important;
}

.react-datepicker__close-icon::after {
  background-color: transparent;
  color: red;
  font-size: 28px;
  font-weight: bold;
}
