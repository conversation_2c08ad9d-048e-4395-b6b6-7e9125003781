import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function EditCoating() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const { coatingToEdit } = location.state || {};

  const [id, setId] = useState(null);
  const [formData, setFormData] = useState({
    paint_type: "",
    color_type: "",
    cosmetic_surface: "",
    price_classification: "",
    price_by_material: "",
  });

  useEffect(() => {
    if (coatingToEdit) {
      setFormData({
        paint_type: coatingToEdit.paint_type || "",
        color_type: coatingToEdit.color_type || "",
        cosmetic_surface: coatingToEdit.cosmetic_surface || "",
        price_classification: coatingToEdit.price_classification || "",
        price_by_material: coatingToEdit.price_by_material || "",
      });
      setId(coatingToEdit.id || null);
    }
  }, [coatingToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleUpdate = async () => {
    const {
      paint_type,
      color_type,
      cosmetic_surface,
      price_classification,
      price_by_material,
    } = formData;

    // ตัวอย่าง validation ง่ายๆ — ปรับได้ตามต้องการ
    if (
      !paint_type.trim() ||
      !color_type.trim() ||
      !cosmetic_surface.trim() ||
      !price_classification.trim() ||
      !price_by_material.trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const payload = {
        id,
        paint_type,
        color_type,
        cosmetic_surface,
        price_classification,
        price_by_material,
      };

      const response = await axios.put("/api/edit/coating", payload);

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/coating");
      }
    } catch (error) {
      console.error("Error updating data:", error);
      Swal.fire({
        icon: "error",
        title: "Error updating data",
        text: error.response?.data?.error || "Unable to update the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/coating");
  };

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("coating.coatingEdit")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton onClick={handleUpdate} />
          <Canclebutton onClick={handleCancel} />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.paintType")}
              </label>
              <input
                name="paint_type"
                value={formData.paint_type}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.colorType")}
              </label>
              <input
                name="color_type"
                value={formData.color_type}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.cosmeticSurface")}
              </label>
              <input
                name="cosmetic_surface"
                value={formData.cosmetic_surface}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.priceClassification")}
              </label>
              <input
                name="price_classification"
                value={formData.price_classification}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.priceByMaterial")}
              </label>
              <input
                name="price_by_material"
                value={formData.price_by_material}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>
        </div>
      </div>

      {/* ตารางแสดง preview */}
      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("coating.paintType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("coating.colorType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("coating.cosmeticSurface")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("coating.priceClassification")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("coating.priceByMaterial")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-[#E9EDF9]">
              <td className="border border-gray-300 px-4 py-1">
                {formData.paint_type}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.color_type}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.cosmetic_surface}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.price_classification}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.price_by_material}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default EditCoating;
