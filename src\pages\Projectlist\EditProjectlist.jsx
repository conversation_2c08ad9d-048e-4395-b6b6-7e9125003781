import React, { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Swal from "sweetalert2";
import axios from "axios";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

// React-icons
import { AiTwotoneCalendar } from "react-icons/ai";

function EditProjectlist() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const formContainerRef = useRef(null);
  const [scale, setScale] = useState(1);
  const { projectToEdit } = location.state || {};

  // Add refs for all datepickers
  const orderDateRef = useRef(null);
  const specifiedDeliveryDateRef = useRef(null);
  const scheduledShipDateRef = useRef(null);
  const processingCompletionDateRef = useRef(null);
  const specifiedDeliveryDate2Ref = useRef(null);
  const scheduledShipDate2Ref = useRef(null);
  const closingDate1Ref = useRef(null);
  const dataEntryDateRef = useRef(null);

  const [formData, setFormData] = useState({
    id: null,
    order_no: "",
    order_date: null,
    delivery_date: null,
    scheduled_ship_date: null,
    deposit_category: 0,
    limited_express: 0,
    customer_id: "",
    rank: "",
    department: "",
    warehouse: "",
    manager_no: "",
    manager_name: "",
    delivery_no: "",
    delivery_name: "",
    transaction_no: "",
    transaction_name: "",
    document_type: "",
    process_status: [],
    site_name: "",
    recipient_contact: "",
    processing_completion_date: null,
    material1: "",
    material2: "",
    material3: "",
    material4: "",
    material5: "",
    material6: "",
    cost: "",
    gross_profit: "",
    quote_no: "",
    closing_date1: null,
    remarks: "",
    remarks2: "",
    data_entry_person: "",
    data_entry_date: null,
    orderNo2: "",
    customerName: "",
    customerLastName: "",
    ProcessingCompletionStatus1: "",
    ProcessingCompletionStatus2: "",
    projectNo: "",
    classification: "",
    productCode: "",
    retailPrice: "",
    productName: "",
    taxPercentage: 0.0,
    orderQuantity: "",
    orderPrice: "",
    unitPriceUpdate: "",
    orderAmount: "",
    orderTaxAmount: "",
    unit: "",
    allocatedQuantity: "",
    costUnitPrice: "",
    costAmount: "",
    arrangementCode: "",
    supplier: "",
    amount: "",
    tax: "",
    total: "",
    deliveryCourse: "",
    soldOut: false,
    shipmentComplete: false,
    stickyNote: false,
    specifiedDeliveryDate2: null,
    scheduledShipDate2: null,
    grossProfitRate: 0.0,
  });

  // Load project data from location.state
  useEffect(() => {
    if (projectToEdit) {
      const parseDate = (dateString) => {
        if (!dateString) return null;
        return new Date(dateString);
      };

      setFormData({
        id: projectToEdit.id || null,
        order_no: projectToEdit.order_no || "",
        order_date: parseDate(projectToEdit.order_date),
        delivery_date: parseDate(projectToEdit.delivery_date),
        scheduled_ship_date: parseDate(projectToEdit.scheduled_ship_date),
        deposit_category: projectToEdit.deposit_category || 0,
        limited_express: projectToEdit.limited_express || 0,
        customer_id: projectToEdit.customer_id || "",
        rank: projectToEdit.rank || "",
        department: projectToEdit.department || "",
        warehouse: projectToEdit.warehouse || "",
        manager_no: projectToEdit.manager_no || "",
        manager_name: projectToEdit.manager_name || "",
        delivery_no: projectToEdit.delivery_no || "",
        delivery_name: projectToEdit.delivery_name || "",
        transaction_no: projectToEdit.transaction_no || "",
        transaction_name: projectToEdit.transaction_name || "",
        document_type: projectToEdit.document_type || "",
        process_status: projectToEdit.process_status || [],
        site_name: projectToEdit.site_name || "",
        recipient_contact: projectToEdit.recipient_contact || "",
        processing_completion_date: parseDate(projectToEdit.processing_completion_date),
        material1: projectToEdit.material1 || "",
        material2: projectToEdit.material2 || "",
        material3: projectToEdit.material3 || "",
        material4: projectToEdit.material4 || "",
        material5: projectToEdit.material5 || "",
        material6: projectToEdit.material6 || "",
        cost: projectToEdit.cost || "",
        gross_profit: projectToEdit.gross_profit || "",
        quote_no: projectToEdit.quote_no || "",
        closing_date1: projectToEdit.closing_date1 ? new Date(projectToEdit.closing_date1) : null,
        remarks: projectToEdit.remarks || "",
        remarks2: projectToEdit.remarks2 || "",
        data_entry_person: projectToEdit.data_entry_person || "",
        data_entry_date: parseDate(projectToEdit.data_entry_date),
        orderNo2: projectToEdit.orderNo2 || "",
        customerName: projectToEdit.customerName || "",
        customerLastName: projectToEdit.customerLastName || "",
        ProcessingCompletionStatus1:projectToEdit.ProcessingCompletionStatus1 || "",
        ProcessingCompletionStatus2:projectToEdit.ProcessingCompletionStatus2 || "",
        projectNo: projectToEdit.projectNo || "",
        classification: projectToEdit.classification || "",
        productCode: projectToEdit.productCode || "",
        retailPrice: projectToEdit.retailPrice || "",
        productName: projectToEdit.productName || "",
        taxPercentage: projectToEdit.taxPercentage || 0.0,
        orderQuantity: projectToEdit.orderQuantity || "",
        orderPrice: projectToEdit.orderPrice || "",
        unitPriceUpdate: projectToEdit.unitPriceUpdate || "",
        orderAmount: projectToEdit.orderAmount || "",
        orderTaxAmount: projectToEdit.orderTaxAmount || "",
        unit: projectToEdit.unit || "",
        allocatedQuantity: projectToEdit.allocatedQuantity || "",
        costUnitPrice: projectToEdit.costUnitPrice || "",
        costAmount: projectToEdit.costAmount || "",
        arrangementCode: projectToEdit.arrangementCode || "",
        supplier: projectToEdit.supplier || "",
        amount: projectToEdit.amount || "",
        tax: projectToEdit.tax || "",
        total: projectToEdit.total || "",
        deliveryCourse: projectToEdit.deliveryCourse || "",
        soldOut: projectToEdit.soldOut || false,
        shipmentComplete: projectToEdit.shipmentComplete || false,
        stickyNote: projectToEdit.stickyNote || false,
        specifiedDeliveryDate2: parseDate(projectToEdit.specifiedDeliveryDate2),
        scheduledShipDate2: parseDate(projectToEdit.scheduledShipDate2),
        grossProfitRate: projectToEdit.grossProfitRate || 0.0,
      });
    }
  }, [location.state]);

  // Handle window resize and adjust scale
  useEffect(() => {
    let timeoutId;

    const handleResize = () => {
      // Debounce resize events to prevent excessive re-renders
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        if (formContainerRef.current) {
          const containerWidth = formContainerRef.current.offsetWidth;
          const formWidth = 1024;
          const newScale = Math.min(1, containerWidth / formWidth);

          // Only update scale if it actually changed (with small tolerance for floating point precision)
          setScale((prevScale) => {
            if (Math.abs(prevScale - newScale) > 0.001) {
              return newScale;
            }
            return prevScale;
          });
        }
      }, 100); // 100ms debounce
    };

    handleResize(); // Initial calculation
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
      clearTimeout(timeoutId);
    };
  }, []);

  const handleChange = useCallback((e) => {
    const { name, type, value, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;

    setFormData((prev) => {
      if (prev[name] === newValue) {
        return prev;
      }

      return {
        ...prev,
        [name]: newValue,
      };
    });
  }, []);

  const handleDateChange = useCallback((date, name) => {
    setFormData((prev) => {
      // Only update if the value actually changed
      if (prev[name] === date) {
        return prev;
      }

      return {
        ...prev,
        [name]: date,
      };
    });
  }, []);

  // Separate handler for grossProfitRate to format on blur (when user finishes typing)
  const handleGrossProfitRateBlur = useCallback((e) => {
    const { name, value } = e.target;
    if (name === "grossProfitRate") {
      const formattedValue = parseFloat(value || 0).toFixed(2);
      setFormData((prev) => ({
        ...prev,
        [name]: formattedValue,
      }));
    }
  }, []);

  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true);
    }
  };

  // Formats the date object into 'YYYY-MM-DD' string for the backend
  const formatDateForBackend = (date) => {
    if (!date) return null;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const handleUpdate = async () => {
    try {
      // Prepare the data payload for the API
      const payload = {
        ...formData,
        order_date: formatDateForBackend(formData.order_date),
        delivery_date: formatDateForBackend(formData.delivery_date),
        scheduled_ship_date: formatDateForBackend(formData.scheduled_ship_date),
        processing_completion_date: formatDateForBackend(formData.processing_completion_date),
        specifiedDeliveryDate2: formatDateForBackend(formData.specifiedDeliveryDate2),
        scheduledShipDate2: formatDateForBackend(formData.scheduledShipDate2),
        closing_date1: formatDateForBackend(formData.closing_date1),
        data_entry_date: formatDateForBackend(formData.data_entry_date),
      };

      // console.log("Sending update payload:", payload);

      // Send PUT request to update projectlist using axios
      const response = await axios.put("/api/edit/projectlist", payload);

      // Check if the response status indicates success
      if (response.status === 200) {
        // Success notification
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          text: "Project list has been updated successfully.",
          showConfirmButton: false,
          timer: 1500,
        });

        navigate("/project-list");
      }
    } catch (error) {
      console.error("Error updating project list:", error);

      // Error notification
      Swal.fire({
        icon: "error",
        title: "An error occurred while updating.",
        text:
          error.response?.data?.error ||
          error.message ||
          "Unable to update the project list.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/project-list");
  };

  return (
    <div className="w-full projectlist-page">
      <div className="flex justify-between items-center mb-4">
        <h1
          data-testid="text-editFormProjectlist-projectlist"
          className="text-lg sm:text-xl font-bold"
        >
          Project List (Edit)
        </h1>
        <div className="flex gap-2">
          <SaveButton
            dataTestId="button-editFormProjectlist-saveProjectlist"
            onClick={handleUpdate}
          />
          <CancelButton
            dataTestId="button-editFormProjectlist-cancelProjectlist"
            onClick={handleCancel}
          />
        </div>
      </div>

      <div ref={formContainerRef} className="w-full">
        <div
          style={{
            transform: `scale(${scale})`,
            transformOrigin: "top left",
            transition: "transform 0.3s ease",
            width: `${(1 / scale) * 100}%`,
          }}
        >
          {/* First row - Order No. */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white p-1 min-w-[100px] text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-orderNo">
                    {t("project.order")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-orderNo"
                  type="text"
                  name="order_no"
                  value={formData.order_no}
                  onChange={handleChange}
                  className="flex-1 min-w-[50px] px-1"
                />
                <button
                  data-testid="button-createFormProjectlist-repeat"
                  name="repeat"
                  type="button"
                  onClick={() =>
                    setFormData((prev) => ({
                      ...prev,
                      quote_no: prev.order_no,
                    }))
                  }
                  className="bg-gray-200 hover:bg-gray-300 px-1 py-1 border-l border-black text-sm whitespace-nowrap min-w-[60px]"
                >
                  {t("project.repeat")}
                </button>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-orderDate">
                    {t("project.orderDate")}
                  </span>
                </div>
                <div className="relative">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-orderDate"
                    ref={orderDateRef}
                    selected={formData.order_date}
                    onChange={(date) => handleDateChange(date, "order_date")}
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="dd/MM/yyyy"
                    className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-7"
                    isClearable={formData.order_date !== null}
                  />
                  {!formData.order_date && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() => handleOpenDatePicker(orderDateRef)}
                    />
                  )}
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-2 text-center text-xs whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-deliveryDate">
                    {t("project.specDate")}
                  </span>
                </div>
                <div className="relative w-full">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-deliveryDate"
                    ref={specifiedDeliveryDateRef}
                    selected={formData.delivery_date}
                    onChange={(date) => handleDateChange(date, "delivery_date")}
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="dd/MM/yyyy"
                    className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-7"
                    isClearable={formData.delivery_date !== null}
                  />
                  {!formData.delivery_date && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() =>
                        handleOpenDatePicker(specifiedDeliveryDateRef)
                      }
                    />
                  )}
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[130px] p-2 text-center text-xs whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-shipDate">
                    {t("project.shipDate")}
                  </span>
                </div>
                <div className="relative w-full">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-shipDate"
                    ref={scheduledShipDateRef}
                    selected={formData.scheduled_ship_date}
                    onChange={(date) =>
                      handleDateChange(date, "scheduled_ship_date")
                    }
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="dd/MM/yyyy"
                    className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-9"
                    isClearable={formData.scheduled_ship_date !== null}
                  />
                  {!formData.scheduled_ship_date && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() => handleOpenDatePicker(scheduledShipDateRef)}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Second row - Quote No. */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white p-1 min-w-[100px] text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-quoteNo">
                    {t("project.quoteNo")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-quoteNo"
                  type="text"
                  name="quote_no"
                  value={formData.quote_no}
                  onChange={handleChange}
                  className="flex-1 min-w-[50px] px-1"
                />
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-customer">
                    {t("project.customer")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-customer"
                    type="text"
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleChange}
                    className="border-r border-black w-[40%] px-1"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-customer"
                    type="text"
                    name="customerLastName"
                    value={formData.customerLastName}
                    onChange={handleChange}
                    className="w-[60%] px-1"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-rank">
                    {t("project.rank")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-rank"
                  type="text"
                  name="rank"
                  value={formData.rank}
                  onChange={handleChange}
                  className="flex-1 px-1"
                />
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[130px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-closingDate1">
                    {t("project.closingDate1")}
                  </span>
                </div>
                <div className="flex">
                  <div className="relative w-full">
                    <DatePicker
                      data-testid="datepicker-editFormProjectlist-closingDate1"
                      ref={closingDate1Ref}
                      selected={formData.closing_date1}
                      onChange={(date) =>
                        handleDateChange(date, "closing_date1")
                      }
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false}
                      dateFormat="dd/MM/yyyy"
                      className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-7"
                      isClearable={formData.closing_date1 !== null}
                    />
                    {!formData.closing_date1 && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                        size={18}
                        onClick={() => handleOpenDatePicker(closingDate1Ref)}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Third row - Deposit Category*/}
          <div className="grid grid-cols-12 gap-1">
            {/* Deposit Category */}
            <div className="col-span-3">
              <div className="flex flex-col border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-depositCategory">
                    {t("project.depositCategory")}
                  </span>
                </div>
                <div className="flex flex-1">
                  <label className="flex items-center justify-start px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-depositRadio1"
                      type="radio"
                      name="deposit_category"
                      value={0}
                      checked={formData.deposit_category === 0}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-depositText0">
                      0
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 border-x border-black bg-purple-700 text-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-depositRadio2"
                      type="radio"
                      name="deposit_category"
                      value={1}
                      checked={formData.deposit_category === 1}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-depositText0Normal">
                      {t("project.depositAmount0")}
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-depositRadio3"
                      type="radio"
                      name="deposit_category"
                      value={2}
                      checked={formData.deposit_category === 2}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-depositText1Deposit">
                      {t("project.depositAmount1")}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Manager */}
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-manager">
                    {t("project.manager")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-manager"
                    type="text"
                    name="manager_no"
                    value={formData.manager_no}
                    onChange={handleChange}
                    className="border-r border-black w-[40%] px-1"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-manager"
                    type="text"
                    name="manager_name"
                    value={formData.manager_name}
                    onChange={handleChange}
                    className="w-[60%] px-1"
                  />
                </div>
              </div>
            </div>

            {/* Department */}
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-department">
                    {t("project.department")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-department"
                  type="text"
                  name="department"
                  value={formData.department}
                  onChange={handleChange}
                  className="flex-1 px-1"
                />
              </div>
            </div>
          </div>

          {/* Fourth row - Limited express */}
          <div className="grid grid-cols-12 gap-x-1">
            {/* Limited express classification */}
            <div className="col-span-3">
              <div className="flex flex-col border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-limitedExpress">
                    {t("project.limitedExpressClass")}
                  </span>
                </div>
                <div className="flex flex-1">
                  <label className="flex items-center justify-start px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-limitedRadio1"
                      type="radio"
                      name="limited_express"
                      value={0}
                      checked={formData.limited_express === 0}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-limitedText0">
                      0
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 border-x border-black bg-purple-700 text-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-limitedRadio2"
                      type="radio"
                      name="limited_express"
                      value={1}
                      checked={formData.limited_express === 1}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-limitedText0None">
                      {t("project.none")}
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-limitedRadio3"
                      type="radio"
                      name="limited_express"
                      value={2}
                      checked={formData.limited_express === 2}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-limitedText1Yes">
                      {t("project.yes")}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Delivery destination */}
            <div className="col-span-3 -mt-4">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm min-w-[140px] p-1.5 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-deliveryDes">
                    {t("project.deliveryDestination")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-deliveryDes"
                    type="text"
                    name="delivery_no"
                    value={formData.delivery_no}
                    onChange={handleChange}
                    className="border-r border-black w-[40%] px-1"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-deliveryDes"
                    type="text"
                    name="delivery_name"
                    value={formData.delivery_name}
                    onChange={handleChange}
                    className="w-[60%] px-1"
                  />
                </div>
              </div>
            </div>

            {/* Warehouse */}
            <div className="col-span-3 -mt-4">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-warehouse">
                    {t("project.warehouse")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-warehouse"
                  type="text"
                  name="warehouse"
                  value={formData.warehouse}
                  onChange={handleChange}
                  className="flex-1 px-1"
                />
              </div>
            </div>
          </div>

          {/* Fifth row - Transaction Classification*/}
          <div className="grid grid-cols-12 gap-x-1 gap-y-0">
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-customerNo">
                    {t("project.customerNo")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-customerNo"
                    type="text"
                    name="customer_id"
                    value={formData.customer_id}
                    onChange={handleChange}
                    className="border-black px-1"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-xs min-w-[140px] px-1 py-2 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-transactionClass">
                    {t("project.transClass")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input1-editFormProjectlist-transactionClass"
                    type="text"
                    name="transaction_no"
                    value={formData.transaction_no}
                    onChange={handleChange}
                    className="border-r border-black w-[40%] px-1"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-transactionClass"
                    type="text"
                    name="transaction_name"
                    value={formData.transaction_name}
                    onChange={handleChange}
                    className="w-[60%] px-1"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-documentType">
                    {t("project.docsType")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-documentType"
                    type="text"
                    name="document_type"
                    value={formData.document_type}
                    onChange={handleChange}
                    className="border-black px-1"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-xs min-w-[130px] text-center whitespace-break-spaces">
                  <span data-testid="text-editFormProjectlist-processCompletion">
                    {t("project.status")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-processCompletion"
                    type="text"
                    name="ProcessingCompletionStatus1"
                    value={formData.ProcessingCompletionStatus1}
                    onChange={handleChange}
                    className="border-r border-black w-[40%] px-1"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-processCompletion"
                    type="text"
                    name="ProcessingCompletionStatus2"
                    value={formData.ProcessingCompletionStatus2}
                    onChange={handleChange}
                    className="w-[60%] px-1"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sixth row - Site name */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-start-1 col-span-6">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm min-w-[140px] px-1 py-2 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-siteName">
                    {t("project.siteName")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-siteName"
                    type="text"
                    name="site_name"
                    value={formData.site_name}
                    onChange={handleChange}
                    className="w-[100%] px-1"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1.5 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-recipientContact">
                    {t("project.recipientContact")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-recipientContact"
                    type="text"
                    name="recipient_contact"
                    value={formData.recipient_contact}
                    onChange={handleChange}
                    className="border-black px-1"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[130px] text-center p-0.5 text-xs whitespace-break-spaces">
                  <span data-testid="text-editFormProjectlist-processingDate">
                    {t("project.cmpdate")}
                  </span>
                </div>
                <div className="relative w-full">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-processingDate"
                    ref={processingCompletionDateRef}
                    selected={formData.processing_completion_date}
                    onChange={(date) =>
                      handleDateChange(date, "processing_completion_date")
                    }
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="dd/MM/yyyy"
                    className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-9"
                    isClearable={formData.processing_completion_date !== null}
                  />
                  {!formData.processing_completion_date && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() =>
                        handleOpenDatePicker(processingCompletionDateRef)
                      }
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Seventh row - Process Status */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-12">
              <div className="flex w-full h-10 border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white px-1 flex items-center justify-center min-w-[140px]">
                  <span data-testid="text-editFormProjectlist-process">
                    {t("project.process")}
                  </span>
                </div>
                {/* Process 1-9 */}
                {[
                  { num: "1", name: t("project.Shirring") },
                  { num: "2", name: t("project.Processing") },
                  { num: "3", name: t("project.Vendor") },
                  { num: "4", name: t("project.Welding") },
                  { num: "5", name: t("project.Finishing") },
                  { num: "6", name: t("project.Color") },
                  { num: "7", name: t("project.Rust Prevention") },
                  { num: "8", name: t("project.Vibration") },
                  { num: "9", name: t("project.Completion") },
                ].map((process) => (
                  <React.Fragment key={process.num}>
                    <div className="flex items-center justify-center px-1 border-r border-black">
                      <input
                        data-testid={`checkbox-editFormProjectlist-${process.num}`}
                        type="checkbox"
                        name="processStatus"
                        value={process.num}
                        checked={formData.process_status.includes(process.num)}
                        onChange={(e) => {
                          const value = e.target.value;
                          setFormData((prev) => ({
                            ...prev,
                            process_status: e.target.checked
                              ? [...prev.process_status, value]
                              : prev.process_status.filter(
                                  (item) => item !== value
                                ),
                          }));
                        }}
                        className="size-4 rounded-lg"
                      />
                    </div>
                    <div className="bg-gray-200 flex items-center justify-center w-[10%] border-r border-black text-xs">
                      <span className="mr-2">{process.num}</span>
                      <span
                        data-testid={`text-editFormProjectlist-${process.num}`}
                      >
                        {process.name}
                      </span>
                    </div>
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>

          {/* Eighth row - Material */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-8">
              <div className="flex w-full border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white px-1 py-10 flex items-center justify-center min-w-[140px]">
                  <span data-testid="text-editFormProjectlist-material">
                    {t("project.material")}
                  </span>
                </div>
                <div className="flex flex-col w-full">
                  <input
                    data-testid="input1-editFormProjectlist-material"
                    type="text"
                    name="material1"
                    value={formData.material1}
                    onChange={handleChange}
                    className="border-b border-black px-1 py-1"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-material"
                    type="text"
                    name="material2"
                    value={formData.material2}
                    onChange={handleChange}
                    className="border-b border-black px-1 py-1"
                  />
                  <input
                    data-testid="input3-editFormProjectlist-material"
                    type="text"
                    name="material3"
                    value={formData.material3}
                    onChange={handleChange}
                    className="border-b border-black px-1 py-1"
                  />
                  <input
                    data-testid="input4-editFormProjectlist-material"
                    type="text"
                    name="material4"
                    value={formData.material4}
                    onChange={handleChange}
                    className="border-b border-black px-1 py-1"
                  />
                  <input
                    data-testid="input5-editFormProjectlist-material"
                    type="text"
                    name="material5"
                    value={formData.material5}
                    onChange={handleChange}
                    className="border-b border-black px-1 py-1"
                  />
                  <input
                    data-testid="input6-editFormProjectlist-material"
                    type="text"
                    name="material6"
                    value={formData.material6}
                    onChange={handleChange}
                    className="px-1 py-1"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Product Information Grid */}
          <div className="my-5">
            <div className="border border-black rounded-sm overflow-hidden">
              <div
                className="grid"
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(14, minmax(60px, 1fr))",
                  width: "100%",
                  maxWidth: "100%",
                }}
              >
                {/* Row 1 - Main Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-black row-span-4 text-sm flex items-center justify-center text-center"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-b  border-black row-span-3 flex items-center justify-center text-center text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-classification">
                    {t("project.classification")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-productCode">
                    {t("project.prodCode")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-retailPrce">
                    {t("project.retailPrce")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <span data-testid="text-editFormProjectlist-remarks">
                    {t("project.remarks")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <span data-testid="text-editFormProjectlist-deliveryDate2">
                    {t("project.specDeliDate")}
                  </span>
                </div>

                {/* Row 2 - Sub Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 3" }}
                >
                  <span data-testid="text-editFormProjectlist-productName">
                    {t("project.pdName")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-tax">
                    {t("project.tax")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderQuantity">
                    {t("project.odQty")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderPrice">
                    {t("project.odPrce")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-unitPriceUpdate">
                    {t("project.unitUp")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderAmount">
                    {t("project.odAmount")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-[11px] flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderTaxAmount">
                    {t("project.odTaxAmount")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <span data-testid="text-editFormProjectlist-shipDate2">
                    {t("project.schShipDate")}
                  </span>
                </div>

                {/* Row 3 - Third Level Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-unit">
                    {t("project.unit")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-allocatedQuantity">
                    {t("project.allocatedQty")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-costUnitPrice">
                    {t("project.csUnitPrce")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-costAmount">
                    {t("project.csAmount")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderNo2">
                    {t("project.order")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-soldOut">
                    {t("project.soldOut")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-shipmentComplete">
                    {t("project.shipmentComplete")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-stickyNote">
                    {t("project.stickyNote")}
                  </span>
                </div>

                {/* Row 4 - Fourth Level Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-arrangementCode">
                    {t("project.arrangementCode")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <span data-testid="text-editFormProjectlist-supplier">
                    {t("project.supplier")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 7" }}
                ></div>

                {/* Product Information Grid Form */}
                {/* Row 1 - Main Headers */}
                <div
                  className="bg-gray-100 border-r border-black row-span-4 text-sm flex items-center justify-center text-center"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    name="projectNo"
                    value={formData.projectNo}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black row-span-3 flex items-center justify-center text-center text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-classification"
                    name="classification"
                    type="text"
                    value={formData.classification}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-productCode"
                    name="productCode"
                    type="text"
                    value={formData.productCode}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-retailPrice"
                    name="retailPrice"
                    type="number"
                    value={formData.retailPrice}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-remarks"
                    name="remarks"
                    type="text"
                    value={formData.remarks}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="border-r border-t border-black text-sm flex items-center text-end py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <div className="relative w-full">
                    <DatePicker
                      data-testid="datepicker-editFormProjectlist-deliveryDate2"
                      ref={specifiedDeliveryDate2Ref}
                      selected={formData.specifiedDeliveryDate2}
                      onChange={(date) =>
                        handleDateChange(date, "specifiedDeliveryDate2")
                      }
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false}
                      dateFormat="yyyy/MM/dd"
                      className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-9"
                      isClearable={formData.specifiedDeliveryDate2 !== null}
                    />
                    {!formData.specifiedDeliveryDate2 && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                        size={16}
                        onClick={() =>
                          handleOpenDatePicker(specifiedDeliveryDate2Ref)
                        }
                      />
                    )}
                  </div>
                </div>

                {/* Row 2 - Sub Headers */}
                <div
                  className="bg-gray-100 border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 3" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-productName"
                    name="productName"
                    type="text"
                    value={formData.productName}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-tax"
                    name="taxPercentage"
                    type="number"
                    value={formData.taxPercentage}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderQuantity"
                    name="orderQuantity"
                    type="number"
                    value={formData.orderQuantity}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderPrice"
                    name="orderPrice"
                    type="number"
                    value={formData.orderPrice}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-unitPriceUpdate"
                    name="unitPriceUpdate"
                    type="number"
                    value={formData.unitPriceUpdate}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderAmount"
                    name="orderAmount"
                    type="number"
                    value={formData.orderAmount}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderTaxAmount"
                    name="orderTaxAmount"
                    type="number"
                    value={formData.orderTaxAmount}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="border-r border-t border-black text-sm flex items-center text-end py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <div className="relative w-full">
                    <DatePicker
                      data-testid="datepicker-editFormProjectlist-shipDate2"
                      ref={scheduledShipDate2Ref}
                      selected={formData.scheduledShipDate2}
                      onChange={(date) =>
                        handleDateChange(date, "scheduledShipDate2")
                      }
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false}
                      dateFormat="yyyy/MM/dd"
                      className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-9"
                      isClearable={formData.scheduledShipDate2 !== null}
                    />
                    {!formData.scheduledShipDate2 && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                        size={16}
                        onClick={() =>
                          handleOpenDatePicker(scheduledShipDate2Ref)
                        }
                      />
                    )}
                  </div>
                </div>

                {/* Row 3 - Third Level Headers */}
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-unit"
                    name="unit"
                    type="text"
                    value={formData.unit}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-allocatedQuantity"
                    name="allocatedQuantity"
                    type="number"
                    value={formData.allocatedQuantity}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-costUnitPrice"
                    name="costUnitPrice"
                    type="number"
                    value={formData.costUnitPrice}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-costAmount"
                    name="costAmount"
                    type="number"
                    value={formData.costAmount}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderNo2"
                    name="orderNo2"
                    type="text"
                    value={formData.orderNo2}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-soldOut"
                    name="soldOut"
                    type="checkbox"
                    checked={formData.soldOut}
                    onChange={handleChange}
                    className="size-4 rounded-lg"
                    title="Sold out"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-shipmentComplete"
                    name="shipmentComplete"
                    type="checkbox"
                    checked={formData.shipmentComplete}
                    onChange={handleChange}
                    className="size-4 rounded-lg"
                    title="Shipment complete"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-stickyNote"
                    name="stickyNote"
                    type="checkbox"
                    checked={formData.stickyNote}
                    onChange={handleChange}
                    className="size-4 rounded-lg"
                    title="Sticky note"
                  />
                </div>

                {/* Row 4 - Fourth Level Headers */}
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-arrangementCode"
                    name="arrangementCode"
                    type="text"
                    value={formData.arrangementCode}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-supplier"
                    name="supplier"
                    type="text"
                    value={formData.supplier}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 7" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>

                {/* Row 5 - Fifth Level Footer */}
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 1" }}
                >
                  <span className="text-white text-sm">
                    <span data-testid="text-editFormProjectlist-remarks2">
                      {t("project.remarks")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 4" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-remarks2"
                    name="remarks2"
                    type="text"
                    value={formData.remarks2}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 2" }}
                >
                  <span className="text-white text-sm">
                    <span data-testid="text-editFormProjectlist-AmountTaxTotal">
                      {t("project.total")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input1-editFormProjectlist-AmountTaxTotal"
                    name="amount"
                    type="number"
                    value={formData.amount}
                    onChange={handleChange}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input2-editFormProjectlist-AmountTaxTotal"
                    name="tax"
                    type="number"
                    value={formData.tax}
                    onChange={handleChange}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input3-editFormProjectlist-AmountTaxTotal"
                    name="total"
                    type="number"
                    value={formData.total}
                    onChange={handleChange}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-[#4472C4] border-r border-t border-b border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 1" }}
                >
                  <span className="text-white text-xs">
                    <span data-testid="text-editFormProjectlist-dateEntryPerson">
                      {t("project.dataEntryPerson")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-b border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input1-editFormProjectlist-dataEntryPerson"
                    name="data_entry_person"
                    type="text"
                    value={formData.data_entry_person}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="border-r border-t border-b border-black text-sm flex items-center justify-center text-center"
                  style={{ gridColumn: "span 2" }}
                >
                  <div className="relative w-full">
                    <DatePicker
                      data-testid="datepicker-editFormProjectlist-dataEntryDate"
                      ref={dataEntryDateRef}
                      selected={formData.data_entry_date}
                      onChange={(date) =>
                        handleDateChange(date, "data_entry_date")
                      }
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false}
                      dateFormat="dd/MM/yyyy"
                      className="flex-1 w-full h-full border-none outline-none px-1 pt-1 pr-9"
                      isClearable={formData.data_entry_date !== null}
                    />
                    {!formData.data_entry_date && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                        size={18}
                        onClick={() => handleOpenDatePicker(dataEntryDateRef)}
                      />
                    )}
                  </div>
                </div>

                {/* Row 6 - Sixth Level Footer */}
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 1" }}
                >
                  <span className="text-white text-sm">
                    <span data-testid="text-editFormProjectlist-deliveryCourse">
                      {t("project.deliveryCourse")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 4" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-deliveryCourse"
                    name="deliveryCourse"
                    type="text"
                    value={formData.deliveryCourse}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 2" }}
                >
                  <span className="text-white text-xs">
                    <span data-testid="text-editFormProjectlist-CostGrossProfitGrossProfitRate">
                      {t("project.totalCost")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input1-editFormProjectlist-CostGrossProfitGrossProfitRate"
                    name="cost"
                    type="number"
                    value={formData.cost}
                    onChange={handleChange}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input2-editFormProjectlist-CostGrossProfitGrossProfitRate"
                    name="gross_profit"
                    type="number"
                    value={formData.gross_profit}
                    onChange={handleChange}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <div className="relative w-full h-full">
                    <input
                      data-testid="input3-editFormProjectlist-CostGrossProfitGrossProfitRate"
                      name="grossProfitRate"
                      type="number"
                      value={formData.grossProfitRate}
                      onChange={handleChange}
                      onBlur={handleGrossProfitRateBlur}
                      className="w-full h-full text-end text-xs"
                    />
                    <span
                      data-testid="text-editFormProjectlist-percent"
                      className="bg-gray-200 absolute -right-5 top-4 -translate-y-1/2 border border-black p-1 text-xs text-black"
                    >
                      %
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EditProjectlist;
