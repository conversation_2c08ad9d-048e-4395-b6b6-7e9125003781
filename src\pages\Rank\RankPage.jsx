import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";

function RankPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [ranks, setRanks] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRanks = async () => {
      try {
        const res = await axios.get("/api/fetch/rank");
        setRanks(res.data);
      } catch (error) {
        console.error("Error fetching ranks:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchRanks();
  }, []);

  // Pagination
  const itemsPerPage = 9;
  const totalPages = Math.ceil(ranks.length / itemsPerPage);
  const paginatedRanks = ranks.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateClick = () => {
    navigate("/create-rank");
  };

  const handleEditClick = (id) => {
    const rankToEdit = ranks.find((item) => item.id === id);

    if (!rankToEdit) {
      Swal.fire("Not found", "Rank data not found.", "warning");
      return;
    }

    navigate(`/edit-rank/${id}`, { state: { rankToEdit } });
  };

  const handleDeleteClick = async (id) => {
    const confirm = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this item?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirm.isConfirmed) {
      try {
        const response = await axios.delete("/api/delete/rank", {
          data: { id }, // ส่ง id เป็น payload
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Your data has been deleted.",
            showConfirmButton: false,
            timer: 1000,
          });

          // ลบข้อมูลออกจาก state
          setRanks((prev) => prev.filter((item) => item.id !== id));
        }
      } catch (error) {
        console.error("Error deleting rank:", error);
        Swal.fire("Error", "Failed to delete item.", "error");
      }
    }
  };

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-rank"
          name="textRank"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("rank.rankManagement")}
        </h1>
        <button
          data-testid="button-createForm-rank"
          name="createRank"
          type="button"
          onClick={handleCreateClick}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-rank"
          className="w-full bg-white border-collapse"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.rank") || "Rank"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.sellingPrice") || "Selling price"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.ratePercent") || "Rate (%)"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.curvedObject") || "Curved Object"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.cuttingBoard") || "Cutting board"}
              </th>
              <th className="py-2 px-4 text-center border border-white">
                {t("rank.operation") || "operation"}
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              [...Array(9)].map((_, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  {[...Array(6)].map((_, cellIdx) => (
                    <td key={cellIdx} className="py-2 px-4 border border-white">
                      <div className="h-4 bg-[#E9EDF9] rounded animate-pulse w-full"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : paginatedRanks.length > 0 ? (
              paginatedRanks.map((rank, index) => (
                <tr
                  key={rank.id}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="py-2 px-4 border border-white">
                    {rank.rank_name || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {rank.selling_price || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {rank.rate_percent || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {rank.curvedObject || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {rank.cuttingBoard || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    <div className="flex justify-center gap-2">
                      <EditButton onClick={() => handleEditClick(rank.id)} />
                      <DeleteButton
                        onClick={() => handleDeleteClick(rank.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={ranks.length}
      />
    </div>
  );
}

export default RankPage;
