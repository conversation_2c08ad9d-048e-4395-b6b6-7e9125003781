import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";
import Footer from "../../components/Footer";

function CreateCoating() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [coatingData, setCoatingData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);

  const itemsPerPage = 10;
  const totalPages = Math.ceil(coatingData.length / itemsPerPage);
  const paginatedData = coatingData.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const [formData, setFormData] = useState({
    paint_type: "",
    color_type: "",
    cosmetic_surface: "",
    price_classification: "",
    price_by_material: "",
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get("/api/fetch/coating");
        setCoatingData(response.data);
      } catch (error) {
        console.error("Failed to fetch coating data", error);
        setCoatingData([]);
      }
    };

    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = async (event) => {
    event.preventDefault();

    const {
      paint_type,
      color_type,
      cosmetic_surface,
      price_classification,
      price_by_material,
    } = formData;

    // เช็คค่าว่าง (trim เผื่อมีช่องว่าง)
    if (
      !paint_type.trim() ||
      !color_type.trim() ||
      !cosmetic_surface.trim() ||
      !price_classification.trim() ||
      !price_by_material.trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const response = await axios.post("/api/create/coating", formData);

      if (response.status === 201 || response.status === 200) {
        Swal.fire({
          icon: "success",
          title: "Saved successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/coating");
      }
    } catch (error) {
      console.error("Error saving data:", error);
      Swal.fire({
        icon: "error",
        title: "An error occurred while saving.",
        text: error.response?.data?.error || "Unable to save the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/coating");
  };

  return (
    <div>
      <form onSubmit={handleSave}>
        <div className="flex flex-wrap justify-between items-center w-full">
          <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
            {t("coating.coatingAdd")}
          </h1>

          <div className="flex items-center gap-4 ml-auto mr-4">
            <Savebutton />
            <Canclebutton onClick={handleCancel} />
          </div>
        </div>

        <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
          <div className="flex flex-col gap-4">
            {/* Row 1 */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("coating.paintType")}
                </label>
                <input
                  name="paint_type"
                  value={formData.paint_type}
                  onChange={handleChange}
                  type="text"
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("coating.colorType")}
                </label>
                <input
                  name="color_type"
                  value={formData.color_type}
                  onChange={handleChange}
                  type="text"
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
            </div>

            {/* Row 2 */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("coating.cosmeticSurface")}
                </label>
                <input
                  name="cosmetic_surface"
                  value={formData.cosmetic_surface}
                  onChange={handleChange}
                  type="text"
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("coating.priceClassification")}
                </label>
                <input
                  name="price_classification"
                  value={formData.price_classification}
                  onChange={handleChange}
                  type="text"
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
            </div>

            {/* Row 3 */}
            <div className="flex flex-col items-start gap-2">
              <div className="w-full md:w-96">
                <label className="block font-bold mb-1">
                  {t("coating.priceByMaterial")}
                </label>
                <input
                  name="price_by_material"
                  value={formData.price_by_material}
                  onChange={handleChange}
                  type="text"
                  className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                />
              </div>
            </div>
          </div>
        </div>
      </form>

      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.paintType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.colorType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.cosmeticSurface")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.priceClassification")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.priceByMaterial")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-gray-300 px-4 py-1">
                  {item.paint_type}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.color_type}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.cosmetic_surface}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.price_classification}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.price_by_material}
                </td>
              </tr>
            ))}
            {paginatedData.length === 0 && (
              <tr>
                <td colSpan="9" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={coatingData.length}
      />
    </div>
  );
}

export default CreateCoating;
