import { useState, useEffect } from "react";
import <PERSON>n<PERSON> from "konva";
import { useUndo } from "../../../contexts/UndoContext";
import { v4 as uuidv4 } from "uuid";
import { useTool } from "../../../contexts/ToolContext";
import NumericKeypad from "../Popups/NumericKeypad";

function TextTool({ triggerAdd, stageRef }) {
  const { activeTool } = useTool();
  const { addAction, boxes, setBoxes } = useUndo();
  const [showKeypad, setShowKeypad] = useState(false);
  const [selectedTextBox, setSelectedTextBox] = useState(null);
  const [currentTextValue, setCurrentTextValue] = useState("");

  useEffect(() => {
    if (triggerAdd > 0 && stageRef && stageRef.current) {
      const newBox = {
        id: uuidv4(),
        x: 150 + boxes.length * 30,
        y: 150 + boxes.length * 30,
        text: "0",
        fontSize: 16,
      };

      addAction({ type: "add-text", payload: newBox, shapeType: "text" });

      setBoxes((prev) => [...prev, newBox]);
    }
  }, [triggerAdd]);

  const handleTextClick = (box) => {
    setSelectedTextBox(box);
    setCurrentTextValue(box.text.toString());
    setShowKeypad(true);
  };

  const handleKeypadConfirm = (value) => {
    if (selectedTextBox) {
      // Convert to number if possible
      const numericValue = !isNaN(parseFloat(value))
        ? parseFloat(value)
        : value;

      const onTextBoxEdit = (box) => {
        if (box.id === selectedTextBox.id) {
          return { ...box, text: numericValue.toString() };
        } else {
          return box;
        }
      };

      setBoxes((prev) => prev.map(onTextBoxEdit));
    }

    setShowKeypad(false);
    setSelectedTextBox(null);
    setCurrentTextValue("");
  };

  // Draw text boxes on the stage
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    // Only clear standalone text boxes (not part of pasted groups)
    const textGroups = layer.find(".text-box-group");
    textGroups.forEach((group) => {
      // Skip text boxes that are part of pasted groups
      if (group.hasName && group.hasName("pasted-shape")) {
        return;
      }

      const textId = group.id().replace("text-box-", "");
      // Only destroy text boxes that aren't in our state array
      if (!boxes.some((b) => b.id.toString() === textId)) {
        group.destroy();
      }
    });

    // Import Konva dynamically to avoid React component issues
    if (!Konva) return;

    // Add or update text boxes that aren't part of pasted groups
    boxes.forEach((box) => {
      // Check if this text box already exists on the stage
      const existingGroup = layer.findOne(`#text-box-${box.id}`);
      if (existingGroup) {
        // Update position
        existingGroup.position({
          x: box.x,
          y: box.y,
        });

        // Update text content and font size
        const textNode = existingGroup.findOne("Text");
        if (textNode) {
          textNode.text(box.text);
          textNode.fontSize(box.fontSize || 18);

          // Add click handler for text editing when Selection tool is active
          textNode.off("click tap"); // Remove previous handlers
          textNode.on("click tap", () => {
            // Only allow editing when Selection tool is active (activeTool is null)
            if (activeTool === null) {
              handleTextClick(box);
            }
          });
        }

        // Update rectangle appearance
        const rectNode = existingGroup.findOne("Rect");
        if (rectNode) {
          rectNode.stroke(""); // Remove stroke since we always have text

          // Add click handler for rectangle (for text editing)
          rectNode.off("click tap"); // Remove previous handlers
          rectNode.on("click tap", () => {
            // Only allow editing when Selection tool is active
            if (activeTool === null) {
              handleTextClick(box);
            }
          });
        }

        return;
      }

      // Create new text box
      const group = new Konva.Group({
        x: box.x,
        y: box.y,
        draggable: true,
        name: "text-box-group",
        id: `text-box-${box.id}`,
      });

      // Add drag handlers
      group.on("dragstart", function () {
        this.moveToTop();
      });

      group.on("dragend", function () {
        const newPos = { x: this.x(), y: this.y() };
        setBoxes((prev) =>
          prev.map((b) => (b.id === box.id ? { ...b, ...newPos } : b))
        );
      });

      const rectWidth = 120;
      const rectHeight = 50;

      const rect = new Konva.Rect({
        width: rectWidth,
        height: rectHeight,
        stroke: "",
        strokeWidth: 1,
        cornerRadius: 4,
      });

      const text = new Konva.Text({
        text: box.text,
        fontSize: box.fontSize || 18,
        fill: "black",
        align: "center",
        verticalAlign: "middle",
        width: rectWidth,
        height: rectHeight,
      });

      // คำนวณให้ text อยู่ตรงกลาง rect
      const textSize = text.getSize();
      const textWidth = text.getWidth();
      const textHeight = text.getHeight();
      const textPositionX = (rectWidth - textWidth) / 2;
      const textPositionY = (rectHeight - textHeight) / 2;

      text.position({
        x: textPositionX,
        y: textPositionY,
      });

      // Add click handlers for both text and rectangle
      text.on("click tap", () => {
        // Only allow editing when Selection tool is active
        if (activeTool === null) {
          handleTextClick(box);
        }
      });

      rect.on("click tap", () => {
        // Only allow editing when Selection tool is active
        if (activeTool === null) {
          handleTextClick(box);
        }
      });

      group.add(rect);
      group.add(text);
      layer.add(group);
    });

    layer.draw();
  }, [boxes, activeTool]);

  return (
    <>
      {showKeypad && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-30">
          <NumericKeypad
            initialValue={currentTextValue}
            onConfirm={handleKeypadConfirm}
            onCancel={() => {
              setShowKeypad(false);
              setSelectedTextBox(null);
              setCurrentTextValue("");
            }}
          />
        </div>
      )}
    </>
  );
}

export default TextTool;
