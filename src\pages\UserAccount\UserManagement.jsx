import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";
import { useTranslation } from "react-i18next";
import Swal from "sweetalert2";

function UserManagement() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  // State for form fields
  const [username, setUsername] = useState("");
  const [name1, setName1] = useState(""); // For "Name" (previously Name (Username))
  const [branch, setBranch] = useState("");
  const [userID, setUserID] = useState("");
  const [email, setEmail] = useState("");
  const [department, setDepartment] = useState("");
  const [post, setPost] = useState("");
  const [name2, setName2] = useState(""); // For "Name" (next to surname)
  const [surname, setSurname] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [role, setRole] = useState("");

  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const handleCancel = () => {
    navigate("/user-account");
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    const userData = {
      username: username,
      name1: name1,
      branch,
      user_id: userID,
      email,
      department,
      post,
      name2: name2,
      surname,
      password,
      role,
    };

    // Frontend validation for required fields
    const requiredFields = [
      { value: username, label: t("userAccount.username") },
      { value: name1, label: t("userAccount.name") }, // Updated label for name1
      { value: branch, label: t("userAccount.branch") },
      { value: userID, label: t("userAccount.userID") },
      { value: email, label: t("userAccount.email") },
      { value: department, label: t("userAccount.department") },
      { value: post, label: t("userAccount.post") },
      { value: name2, label: t("userAccount.name") },
      { value: surname, label: t("userAccount.surName") },
      { value: password, label: t("userAccount.password") },
      { value: confirmPassword, label: t("userAccount.confirmPassword") },
      { value: role, label: t("userAccount.role") || "Role" },
    ];

    const missingFields = [];
    requiredFields.forEach((field) => {
      if (typeof field.value === "string" && field.value.trim() === "") {
        missingFields.push(field.label);
      } else if (field.value === null || field.value === undefined) {
        missingFields.push(field.label);
      }
    });

    if (missingFields.length > 0) {
      const errorMessage = `missingFields: ${missingFields.join(", ")}.`;
      setSaveError(errorMessage);
      Swal.fire({
        icon: "error",
        title: t("validation.validationError"),
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    if (password !== confirmPassword) {
      setSaveError("Passwords do not match.");
      Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Passwords do not match.",
      });
      setIsSaving(false);
      return;
    }

    try {
      const response = await fetch("/api/create/useraccount", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("text/html")) {
          const errorText = await response.text();
          console.error("Backend returned HTML error:", errorText);
          throw new Error(
            `Server error (${response.status}): Please check backend logs. Response was HTML.`
          );
        } else {
          const errorData = await response.json();
          throw new Error(
            errorData.error || `HTTP error! status: ${response.status}`
          );
        }
      }

      setSaveSuccess(true);
      Swal.fire({
        icon: "success",
        title: "Save Successfully!",
        showConfirmButton: true,
        confirmButtonText: "OK",
      }).then(() => {
        navigate("/user-account");
      });
    } catch (err) {
      const displayErrorMessage = err.message;
      setSaveError(displayErrorMessage);
      console.error("Error saving user:", err);
      Swal.fire({
        icon: "error",
        title: "saveError!!",
        text: displayErrorMessage,
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2
        data-testid="text-userManagement-create"
        name="textUserManagementcreate"
        className="text-2xl font-bold mb-6"
      >
        {t("userAccount.userAccountList")}
      </h2>
      <form className="grid grid-cols-1 gap-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Row 1: Username & Name (previously Name (Username)) */}
          <div>
            <label
              data-testid="text-userManagement-username"
              name="textUserManagementusername"
              className="block font-semibold mb-1"
            >
              {t("userAccount.username")}
            </label>
            <input
              data-testid="input-userManagement-username"
              name="inputUserManagementusername"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-userManagement-name1"
              name="textUserManagementname1"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")}
            </label>
            <input
              data-testid="input-userManagement-name1"
              name="inputUserManagementname1"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={name1}
              onChange={(e) => setName1(e.target.value)}
            />
          </div>

          {/* Row 2: User ID & Email */}
          <div>
            <label
              data-testid="text-userManagement-userid"
              name="textUserManagementuserid"
              className="block font-semibold mb-1"
            >
              {t("userAccount.userID")}
            </label>
            <input
              data-testid="input-userManagement-userid"
              name="inputUserManagementuserid"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={userID}
              onChange={(e) => setUserID(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-userManagement-email"
              name="textUserManagementemail"
              className="block font-semibold mb-1"
            >
              {t("userAccount.email")}
            </label>
            <input
              data-testid="input-userManagement-email"
              name="inputUserManagementemail"
              type="email"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          {/* Row 3: Branch & Department */}
          <div>
            <label
              data-testid="text-userManagement-branch"
              name="textUserManagementbranch"
              className="block font-semibold mb-1"
            >
              {t("userAccount.branch")}
            </label>
            <select
              data-testid="select-userManagement-branch"
              name="selectUserManagementbranch"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={branch}
              onChange={(e) => setBranch(e.target.value)}
            >
              <option value="">Select an option</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>
          <div>
            <label
              data-testid="text-userManagement-department"
              name="textUserManagementdepartment"
              className="block font-semibold mb-1"
            >
              {t("userAccount.department")}
            </label>
            <select
              data-testid="select-userManagement-department"
              name="selectUserManagementdepartment"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={department}
              onChange={(e) => setDepartment(e.target.value)}
            >
              <option value="">Select an option</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>

          {/* Row 4: Post & Role */}
          <div>
            <label
              data-testid="text-userManagement-post"
              name="textUserManagementpost"
              className="block font-semibold mb-1"
            >
              {t("userAccount.post")}
            </label>
            <select
              data-testid="select-userManagement-post"
              name="selectUserManagementpost"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={post}
              onChange={(e) => setPost(e.target.value)}
            >
              <option value="">Select an option</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>
          <div>
            <label
              data-testid="text-userManagement-role"
              name="textUserManagementrole"
              className="block font-semibold mb-1"
            >
              Role
            </label>
            <select
              data-testid="select-userManagement-role"
              name="selectUserManagementrole"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={role}
              onChange={(e) => setRole(e.target.value)}
            >
              <option value="">Select a role</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
            </select>
          </div>

          {/* Row 5: Name (name2) & Surname */}
          <div>
            <label
              data-testid="text-userManagement-name2"
              name="textUserManagementname2"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")}
            </label>
            <input
              data-testid="input-userManagement-name2"
              name="inputUserManagementname2"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={name2}
              onChange={(e) => setName2(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-userManagement-surname"
              name="textUserManagementsurname"
              className="block font-semibold mb-1"
            >
              {t("userAccount.surName")}
            </label>
            <input
              data-testid="input-userManagement-surname"
              name="inputUserManagementsurname"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={surname}
              onChange={(e) => setSurname(e.target.value)}
            />
          </div>

          {/* Row 6: Password & Confirm Password */}
          <div>
            <label
              data-testid="text-userManagement-password"
              name="textUserManagementpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.password")}
            </label>
            <input
              type="password"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              data-testid="input-userManagement-password"
              name="inputUserManagementpassword"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>
          <div>
            <label
              data-testid="text-userManagement-confirmpassword"
              name="textUserManagementconfirmpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.confirmPassword")}
            </label>
            <input
              data-testid="input-userManagement-confirmpassword"
              name="inputUserManagementconfirmpassword"
              type="password"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>
        </div>
      </form>

      {isSaving && (
        <div className="text-center py-2 text-blue-600">
          {t("action.saving")}...
        </div>
      )}
      {saveError && (
        <div className="text-center py-2 text-red-600">{saveError}</div>
      )}
      {saveSuccess && (
        <div className="text-center py-2 text-green-600">SaveSuccess...</div>
      )}

      <div className="mt-6 flex gap-4">
        <Savebutton
          dataTestId="button-userManagement-saveuser"
          name="buttonUserManagementsaveuser"
          onClick={handleSave}
          disabled={isSaving}
        />
        <Canclebutton
          dataTestId="button-userManagement-cancleuser"
          name="buttonUserManagementcancleuser"
          onClick={handleCancel}
          disabled={isSaving}
        />
      </div>
    </div>
  );
}

export default UserManagement;
