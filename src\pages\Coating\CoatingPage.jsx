import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";

function CoatingPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [coatingData, setCoatingData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(true);

  const itemsPerPage = 10;
  const totalPages = Math.ceil(coatingData.length / itemsPerPage);
  const paginatedCoating = coatingData.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  useEffect(() => {
    const fetchCoating = async () => {
      try {
        const response = await axios.get("/api/fetch/coating");
        setCoatingData(response.data);
      } catch (error) {
        console.error("Error fetching coating data:", error);
        setCoatingData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCoating();
  }, []);

  const handleCreateCoating = () => {
    navigate("/create-coating");
  };

  const handleEditCoating = (id) => {
    const coatingToEdit = coatingData.find((item) => item.id === id);

    if (!coatingToEdit) {
      Swal.fire("Not found", "Coating data not found.", "warning");
      return;
    }

    navigate(`/edit-coating/${id}`, { state: { coatingToEdit } });
  };

  const handleDeleteClick = async (id) => {
    const confirm = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this item?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirm.isConfirmed) {
      try {
        const response = await axios.delete("/api/delete/coating", {
          data: { id }, // ส่ง id ใน body request ตาม backend
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Your data has been deleted.",
            showConfirmButton: false,
            timer: 1000,
          });

          // ลบข้อมูลออกจาก state coatingData โดยกรองเอาอันที่ไม่ใช่ id นี้
          setCoatingData((prev) => prev.filter((item) => item.id !== id));
        }
      } catch (error) {
        console.error("Error deleting coating:", error);
        Swal.fire("Error", "Failed to delete item.", "error");
      }
    }
  };

  return (
    <div className="w-full">
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-coating"
          name="textCoating"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("coating.coating")}
        </h1>
        <button
          data-testid="button-createForm-coating"
          name="createCoating"
          type="button"
          onClick={handleCreateCoating}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full bg-white border-collapse">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="py-2 px-4 text-left border border-white min-w-[50px]">
                {t("coating.No")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.paintType")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.colorType")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.cosmeticSurface")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.priceClassification")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.priceByMaterial")}
              </th>
              <th className="py-2 px-4 text-center border border-white min-w-[120px]">
                {t("coating.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              [...Array(itemsPerPage)].map((_, i) => (
                <tr
                  key={i}
                  className={i % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  {[...Array(7)].map((_, j) => (
                    <td key={j} className="py-2 px-4 border border-white">
                      <div className="h-4 bg-[#E9EDF9] rounded animate-pulse w-full"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : paginatedCoating.length > 0 ? (
              paginatedCoating.map((coating, index) => (
                <tr
                  key={coating.id}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="py-2 px-4 border border-white">
                    {coating.id || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {coating.paint_type || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {coating.color_type || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {coating.cosmetic_surface || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {coating.price_classification || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    {coating.price_by_material || ""}
                  </td>
                  <td className="py-2 px-4 border border-white">
                    <div className="flex justify-center gap-2">
                      <EditButton
                        onClick={() => handleEditCoating(coating.id)}
                        dataTestId="button-editForm-coating"
                      />
                      <DeleteButton
                        dataTestId="button-delete-coating"
                        onClick={() => handleDeleteClick(coating.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="7" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={coatingData.length}
      />
    </div>
  );
}

export default CoatingPage;
