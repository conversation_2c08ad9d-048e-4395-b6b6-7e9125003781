import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";

// React-icons
import { FaSearch } from "react-icons/fa";

function ProjectlistPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [loading, setLoading] = useState(true);

  const [searchTerm, setSearchTerm] = useState("");
  const [searchField, setSearchField] = useState("orderNo");

  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 9;
  const isDeleting = useRef(false);

  useEffect(() => {
    const fetchProjectList = async () => {
      try {
        setLoading(true);
        const res = await axios.get("/api/fetch/projectlist");
        setProjects(res.data);
        setFilteredProjects(res.data);
      } catch (error) {
        console.error("Error fetching project list:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchProjectList();
  }, []);

  useEffect(() => {
    const filtered = projects.filter((item) =>
      item[searchField]
        ?.toString()
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
    );
    setFilteredProjects(filtered);

    // ถ้าไม่ใช่ตอนลบ → รีเซ็ตไปหน้าแรก
    if (!isDeleting.current) {
      setCurrentPage(0);
    } else {
      // reset flag ทิ้ง
      isDeleting.current = false;
    }
  }, [searchTerm, searchField, projects]);

  const totalPages = Math.ceil(filteredProjects.length / itemsPerPage);

  const paginatedProjects = filteredProjects.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateClick = () => {
    navigate("/create-project");
  };

  const handleEditClick = async (orderNo) => {
    try {
      const res = await axios.get(`/api/project/order/${orderNo}`);
      const projectToEdit = res.data;

      if (!projectToEdit) {
        Swal.fire("Not found", "Project not found.", "warning");
        return;
      }

      navigate(`/edit-project/${orderNo}`, { state: { projectToEdit } });
    } catch (error) {
      Swal.fire("Error", "Failed to load project data.", "error");
      console.error("Fetch error:", error);
    }
  };

  const handleDeleteClick = async (id) => {
    const confirm = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this item?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirm.isConfirmed) {
      try {
        const response = await axios.delete("/api/delete/projectlist", {
          data: { id },
        });

        if (response.status === 200) {
          isDeleting.current = true;

          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Your data has been deleted.",
            showConfirmButton: false,
            timer: 1000,
          });

          setProjects((prev) => prev.filter((item) => item.id !== id));
          setFilteredProjects((prev) => prev.filter((item) => item.id !== id));
        }
      } catch (error) {
        console.error("Error deleting project:", error);
        Swal.fire("Error", "Failed to delete item.", "error");
      }
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("project.projectlist")}
        </h1>

        {/* Create Button */}
        <button
          data-testid="button-createForm-projectlist"
          name="createProjectlist"
          type="button"
          onClick={handleCreateClick}
          className="btn-create"
        >
          {t("action.create")}
        </button>
      </div>

      {/* Header Section */}
      <div className="flex flex-row items-center mb-3 gap-2">
        <div className="relative w-full md:w-[300px] lg:w-[600px]">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            data-testid="input-projectlist-search"
            name="projectlistSearch"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={t("project.placeholder")}
            className="w-full pl-10 pr-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <select
          data-testid="select-projectlist-searchType"
          name="projectlistSearchType"
          value={searchField}
          onChange={(e) => setSearchField(e.target.value)}
          className="w-[200px] px-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="orderNo">{t("project.order")}</option>
          <option value="customerAbbr">{t("project.customerAbb")}</option>
          <option value="siteName">{t("project.siteName")}</option>
        </select>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-projectlist"
          className="w-full bg-white border-collapse whitespace-nowrap"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-3 px-4 text-left border border-white min-w-[120px]">
                {t("project.order")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[150px]">
                {t("project.orderDate")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[150px]">
                {t("project.specDate")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[200px]">
                {t("project.customerAbb")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[200px]">
                {t("project.siteName")}
              </th>
              <th className="py-3 px-4 text-center border border-white min-w-[120px]">
                {t("project.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              [...Array(itemsPerPage)].map((_, i) => (
                <tr
                  key={i}
                  className={i % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  {[...Array(6)].map((_, j) => (
                    <td key={j} className="py-2 px-4 border border-white">
                      <div className="h-4 bg-[#E9EDF9] rounded animate-pulse w-full"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : paginatedProjects.length > 0 ? (
              paginatedProjects.map((project, index) => (
                <tr
                  key={project.id}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="py-1.5 px-4 border border-white">
                    {project.orderNo}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {project.orderDate
                      ? new Date(project.orderDate).toLocaleDateString(
                          "th-TH",
                          {
                            timeZone: "Asia/Bangkok",
                            year: "numeric",
                            month: "2-digit",
                            day: "2-digit",
                          }
                        )
                      : ""}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {project.specifiedDeliveryDate
                      ? new Date(
                          project.specifiedDeliveryDate
                        ).toLocaleDateString("th-TH", {
                          timeZone: "Asia/Bangkok",
                          year: "numeric",
                          month: "2-digit",
                          day: "2-digit",
                        })
                      : ""}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {project.customerAbbr}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    {project.siteName}
                  </td>
                  <td className="py-1.5 px-4 border border-white">
                    <div className="flex justify-center gap-4">
                      <EditButton onClick={() => handleEditClick(project.orderNo)} />
                      <DeleteButton
                        onClick={() => handleDeleteClick(project.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredProjects.length}
      />
    </div>
  );
}

export default ProjectlistPage;
