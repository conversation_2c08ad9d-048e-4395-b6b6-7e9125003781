import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Swal from "sweetalert2";
import axios from "axios";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

// React-icons
import { FaPlus, FaMinus } from "react-icons/fa";

function CreateRank() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [formData, setFormData] = useState([
    {
      rank_name: "",
      selling_price: "",
      rate_percent: "",
      curvedObject: "",
      cuttingBoard: "",
    },
  ]);

  const handleChange = (index, field, value) => {
    const updatedForms = [...formData];
    updatedForms[index][field] = value;
    setFormData(updatedForms);
  };

  const handleAddForm = () => {
    setFormData([
      ...formData,
      {
        rank_name: "",
        selling_price: "",
        rate_percent: "",
        curvedObject: "",
        cuttingBoard: "",
      },
    ]);
  };

  const handleRemoveForm = (index) => {
    if (formData.length === 1) return;
    const updatedForms = [...formData];
    updatedForms.splice(index, 1);
    setFormData(updatedForms);
  };

  const handleSave = async (event) => {
    event.preventDefault();
    // Validation
    for (const item of formData) {
      if (
        !item.rank_name.trim() ||
        !item.selling_price.toString().trim() ||
        !item.rate_percent.toString().trim() ||
        !item.curvedObject.toString().trim() ||
        !item.cuttingBoard.toString().trim()
      ) {
        await Swal.fire({
          icon: "warning",
          title: t("Please fill in all fields."),
          confirmButtonText: "OK",
        });
        return;
      }
    }

    try {
      const response = await axios.post("/api/create/rank", formData);

      if (response.status === 200 || response.status === 201) {
        await Swal.fire({
          icon: "success",
          title: t("Save successful!"),
          showConfirmButton: false,
          timer: 1500,
        });
        navigate("/rank");
      } else {
        await Swal.fire({
          icon: "error",
          title: t("Save failed"),
          text: response.statusText || "",
          confirmButtonText: "OK",
        });
      }
    } catch (error) {
      console.error("Error saving ranks:", error);
      await Swal.fire({
        icon: "error",
        title: t("Save failed due to network error."),
        text: error.message,
        confirmButtonText: "OK",
      });
    }
  };

  const handleCancel = () => {
    navigate("/rank");
  };

  return (
    <div className="max-w-5xl mx-auto p-3">
      <h1
        data-testid="text-createFormRank-rank"
        className="text-2xl font-bold mb-6"
      >
        {t("createAndEditRank.rankAdd")}
      </h1>

      <form onSubmit={handleSave}>
        {formData.map((form, index) => (
          <div key={index} className="mb-4">
            <div className="flex items-center gap-2">
              {/* Remove button */}
              <div className="pt-6">
                <button
                  data-testid="button-createFormRank-deleteForm"
                  name="deleteRankForm"
                  type="button"
                  onClick={() => handleRemoveForm(index)}
                  className={`w-10 h-10 flex items-center justify-center rounded-md border border-red-500 text-red-500 ${
                    formData.length === 1 ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  disabled={formData.length === 1}
                >
                  <FaMinus />
                </button>
              </div>

              {/* Rank Name input */}
              <div className="flex flex-col flex-[1] min-w-0">
                <label className="text-sm font-medium text-gray-700 mb-1">
                  {t("createAndEditRank.rank")}
                </label>
                <input
                  data-testid="input-createFormRank-rank"
                  name="rank_name"
                  type="text"
                  value={form.rank_name}
                  onChange={(e) =>
                    handleChange(index, "rank_name", e.target.value)
                  }
                  className="h-10 border border-gray-500 rounded-md px-3 text-center"
                />
              </div>

              {/* Markup Rate input */}
              <div className="flex flex-col flex-[3] min-w-0">
                <label className="text-sm font-medium text-gray-700 mb-1">
                  {t("createAndEditRank.sellingPrice")}
                </label>
                <input
                  data-testid="input-createFormRank-sellingPrice"
                  name="selling_price"
                  type="number"
                  step="0.01"
                  value={form.selling_price}
                  onChange={(e) =>
                    handleChange(index, "selling_price", e.target.value)
                  }
                  className="h-10 border border-gray-500 rounded-md px-3"
                />
              </div>

              {/* Rate Percent input */}
              <div className="flex flex-col flex-[2] min-w-0">
                <label className="text-sm font-medium text-gray-700 mb-1">
                  {t("createAndEditRank.ratePercent")}
                </label>
                <input
                  data-testid="input-createFormRank-rate"
                  name="rate_percent"
                  type="number"
                  step="0.01"
                  value={form.rate_percent}
                  onChange={(e) =>
                    handleChange(index, "rate_percent", e.target.value)
                  }
                  className="h-10 border border-gray-500 rounded-md px-3"
                />
              </div>

              {/* Curved Object input */}
              <div className="flex flex-col flex-[2] min-w-0">
                <label className="text-sm font-medium text-gray-700 mb-1">
                  {t("createAndEditRank.curvedObject")}
                </label>
                <input
                  data-testid="input-createFormRank-curvedObject"
                  name="curvedObject"
                  type="number"
                  value={form.curvedObject}
                  onChange={(e) =>
                    handleChange(index, "curvedObject", e.target.value)
                  }
                  className="h-10 border border-gray-500 rounded-md px-3"
                />
              </div>

              {/* Cutting Board input */}
              <div className="flex flex-col flex-[2] min-w-0">
                <label className="text-sm font-medium text-gray-700 mb-1">
                  {t("createAndEditRank.cuttingBoard")}
                </label>
                <input
                  data-testid="input-createFormRank-cuttingBoard"
                  name="cuttingBoard"
                  type="number"
                  value={form.cuttingBoard}
                  onChange={(e) =>
                    handleChange(index, "cuttingBoard", e.target.value)
                  }
                  className="h-10 border border-gray-500 rounded-md px-3"
                />
              </div>

              {/* Required indicator */}
              <div className="pt-6">
                <span className="text-xl font-bold">*</span>
              </div>
            </div>
          </div>
        ))}

        {/* Add button */}
        <div className="mb-8">
          <button
            data-testid="button-createFormRank-addRankForm"
            name="addRankForm"
            type="button"
            onClick={handleAddForm}
            className="w-10 h-10 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center text-white shadow-md transition-colors duration-200"
          >
            <FaPlus size={18} />
          </button>
        </div>

        {/* Button Group */}
        <div className="flex gap-4 my-5">
          <SaveButton
            type="submit"
            data-testid="button-createFormRank-saveRank"
          />
          <CancelButton
            data-testid="button-createFormRank-cancelRank"
            onClick={handleCancel}
          />
        </div>
      </form>
    </div>
  );
}

export default CreateRank;
