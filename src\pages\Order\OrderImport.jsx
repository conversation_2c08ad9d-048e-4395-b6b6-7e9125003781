import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Swal from 'sweetalert2';

import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { FaSearch } from "react-icons/fa";

function OrderImport() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState("");
  const [searchField, setSearchField] = useState("order_no");

  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 8;

  const fetchOrders = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/fetch/order");
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setOrders(data);
      setFilteredOrders(data);
    } catch (err) {
      console.error("Failed to fetch orders:", err);
      setError(`Error: Failed to fetch orders - ${err.message}`);
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: `Failed to fetch orders: ${err.message}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  useEffect(() => {
    const filtered = orders.filter((item) => {
      let fieldValue = item[searchField];

      // Ensure orderDate and specifiedDeliveryDate are properly handled for search
      if (searchField === 'orderDate' || searchField === 'specifiedDeliveryDate') {
        // Use a more robust check for valid date, if item[searchField] exists and is a valid date string
        fieldValue = fieldValue ? new Date(fieldValue).toLocaleDateString('en-GB') : '';
      }

      const stringFieldValue = fieldValue?.toString().toLowerCase() || '';
      return stringFieldValue.includes(searchTerm.toLowerCase());
    });
    setFilteredOrders(filtered);
    setCurrentPage(0);
  }, [searchTerm, searchField, orders]);

  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);

  const paginatedOrders = filteredOrders.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  ).map(item => ({
    ...item,
    // Ensure orderDate is properly accessed and formatted
    // Use optional chaining (?.) and nullish coalescing (?? '') for robustness
    orderDate: item.orderDate ? new Date(item.orderDate).toLocaleDateString('en-GB') : '',
    specifiedDeliveryDate: item.specifiedDeliveryDate ? new Date(item.specifiedDeliveryDate).toLocaleDateString('en-GB') : '',
  }));


  const handleCreateOrder = () => {
    navigate("/create-order");
  };

  const handleEditOrder = (orderId) => {
    const orderToEdit = orders.find((item) => item.id === orderId);
    if (orderToEdit) {
      navigate(`/edit-order/${orderToEdit.id}`, { state: { orderToEdit } });
    } else {
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: "Order not found for editing.",
      });
    }
  };

  const handleDeleteOrder = async (idToDelete) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel"
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await fetch(`/api/delete/order`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: idToDelete }),
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
          }

          Swal.fire(
            "Deleted!",
            "Your order has been deleted.",
            'success'
          );
          fetchOrders();
        } catch (err) {
          console.error("Failed to delete order:", err);
          Swal.fire(
            "Error!",
            `Delete Error: ${err.message}`,
            'error'
          );
        }
      }
    });
  };

  if (isLoading) {
    return <div className="text-center py-10 text-gray-700">Loading data...</div>;
  }

  if (error) {
    return <div className="text-center py-10 text-red-600">{error}</div>;
  }

  return (
    <div className="w-full">
      <div className="flex items-center mb-3 justify-between">
        <h1 className="text-xl font-bold sm:mb-0 sm:ml-0 lg:ml-20">
          Order Import
        </h1>

        <button
          data-testid="button-createForm-order"
          name="buttonCreateFormorder"
          className="btn-create"
          onClick={handleCreateOrder}
        >
          Create
        </button>
      </div>

      <div className="flex flex-row items-center mb-3 gap-2">
        <div className="relative w-full md:w-[300px] lg:w-[600px]">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            data-testid="input-search-order"
            name="inputSearchorder"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search..."
            className="w-full pl-10 pr-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <select
          data-testid="select-search-order"
          name="selectSearchorder"
          value={searchField}
          onChange={(e) => setSearchField(e.target.value)}
          className="w-[200px] px-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="orderDate">Order Date</option>
          <option value="order_no">Order No.</option>
          <option value="specifiedDeliveryDate">
            Specified Delivery Date
          </option>
          <option value="customerAbbr">Customer Abbr.</option>
          <option value="site_name">Site Name</option>
          <option value="recipient_contact">
            Recipient Contact
          </option>
          <option value="delivery_route">
            Delivery Route
          </option>
          <option value="delivery_dest_abbr">
            Delivery Destination Abbreviation
          </option>
          <option value="contact_name">Contact Name</option>
          <option value="rank">Rank</option>
          <option value="pi">PI</option>
        </select>
      </div>

      <div className="overflow-x-auto">
        <table
          data-testid="table-order"
          name="tableOrder"
          className="w-full bg-white border-collapse text-sm"
        >
          <thead className="text-sm">
            <tr className="bg-[#4472C4] text-white">
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Order Date
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Order No.
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Specified Delivery Date
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Customer Abbr.
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Site Name
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Recipient Contact
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Delivery Route
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px] text-xs">
                Delivery Destination Abbreviation
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Contact Name
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                Rank
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                PI
              </th>
              <th className="py-2 px-2 text-center border border-white min-w-[50px]">
                Operation
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedOrders.map((order, index) => (
              <tr
                key={order.id || `order-${index}`}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="py-1 px-2 border border-white">
                  {order.orderDate}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.order_no}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.specifiedDeliveryDate}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.customerAbbr}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.site_name}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.recipient_contact}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.delivery_route}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.delivery_dest_abbr}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.contact_name}
                </td>
                <td className="py-1 px-2 border border-white">{order.rank}</td>
                <td className="py-1 px-2 border border-white">{order.pi}</td>
                <td className="py-1 px-2 border border-white text-center">
                  <div className="flex justify-center gap-2">
                    <EditButton
                      dataTestId="button-edit-order"
                      name="buttonEditorder"
                      onClick={() => handleEditOrder(order.id)}
                    />
                    <DeleteButton
                      dataTestId="button-delete-order"
                      name="buttonDeleteorder"
                      onClick={() => handleDeleteOrder(order.id)}
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedOrders.length === 0 && !isLoading && !error && (
              <tr>
                <td colSpan="11" className="text-center py-4 text-gray-500">
                  No data found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredOrders.length}
        itemsPerPage={itemsPerPage}
      />
    </div>
  );
}

export default OrderImport;