import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Swal from 'sweetalert2';

import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

import { AiTwotoneCalendar } from "react-icons/ai";

function CreateOrder() {
  const navigate = useNavigate();

  const orderDateRef = useRef(null);
  const specifiedDeliveryDateRef = useRef(null);

  const [formData, setFormData] = useState({
    orderDate: null,
    orderNo: "",
    specifiedDeliveryDate: null,
    customerAbbr: "",
    siteName: "",
    contactName: "",
    recipientContact: "",
    deliveryRoute: "",
    deliveryDestinationAbbreviation: "",
    rank: "",
    pi: "",
  });

  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [customers, setCustomers] = useState([]);
  const [fetchCustomersError, setFetchCustomersError] = useState(null);
  const [ranks, setRanks] = useState([]);
  const [fetchRanksError, setFetchRanksError] = useState(null);

  useEffect(() => {
    // Function to fetch customer data from the API
    const fetchCustomers = async () => {
      try {
        const response = await fetch("/api/fetch/customer");
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setCustomers(data);
      } catch (error) {
        console.error("Error fetching customers:", error);
        setFetchCustomersError(`Failed to load customer data: ${error.message}`);
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: `Failed to load customer data: ${error.message}`,
        });
      }
    };

    // Function to fetch rank data from the API
    const fetchRanks = async () => {
      try {
        const response = await fetch("/api/fetch/rank");
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setRanks(data);
      } catch (error) {
        console.error("Error fetching ranks:", error);
        setFetchRanksError(`Failed to load rank data: ${error.message}`);
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: `Failed to load rank data: ${error.message}`,
        });
      }
    };

    // Fetch data when the component mounts
    fetchCustomers();
    fetchRanks();
  }, []); // Empty dependency array means this effect runs once after the initial render

  // Handles changes for text input fields
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handles changes for DatePicker fields
  const handleDateChange = (date, fieldName) => {
    // Ensure the date is treated as UTC to prevent timezone shifts
    if (date) {
      const utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
      setFormData((prev) => ({
        ...prev,
        [fieldName]: utcDate,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [fieldName]: null, // Allow clearing the date
      }));
    }
  };

  // Opens the DatePicker calendar when the icon is clicked
  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true);
    }
  };

  // Formats the date object into 'YYYY-MM-DD' string for the backend using UTC values
  const formatDateForBackend = (date) => {
    if (!date) return null;
    // Use UTC methods to ensure the date string is consistent regardless of local timezone
    // This function now expects 'date' to already be a UTC Date object from handleDateChange
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const day = String(date.getUTCDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handles the save action when the Save button is clicked
  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);

    // Define required fields for validation
    const requiredFields = [
      { value: formData.orderNo, label: "Order No." },
      { value: formData.customerAbbr, label: "Customer Abbr." },
      { value: formData.specifiedDeliveryDate, label: "Specified Delivery Date" },
      { value: formData.orderDate, label: "Order Date" },
    ];

    // Check for missing required fields
    const missingFields = [];
    requiredFields.forEach(field => {
      if (field.value === null || field.value === undefined || (typeof field.value === 'string' && field.value.trim() === '')) {
        missingFields.push(field.label);
      }
    });

    // If there are missing fields, show an error and stop
    if (missingFields.length > 0) {
      const errorMessage = `Missing fields: ${missingFields.join(', ')}. Please fill in all required fields.`;
      setSaveError(errorMessage);
      Swal.fire({
        icon: 'error',
        title: 'Validation Error!',
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    // Prepare the payload for the API request
    const payload = {
      order_date: formData.orderDate ? formatDateForBackend(formData.orderDate) : null, // Formatted order date using UTC
      order_no: formData.orderNo,
      customer_id: formData.customerAbbr,
      delivery_date: formData.specifiedDeliveryDate ? formatDateForBackend(formData.specifiedDeliveryDate) : null, // Formatted delivery date using UTC
      project_name: null, // Default to null if not provided
      material_id: null, // Default to null if not provided
      quantity: 0, // Default to 0 if not provided
      rank_id: formData.rank ? parseInt(formData.rank, 10) : null, // Parse rank to integer, default to null
      project_id: null, // Default to null if not provided
      site_name: formData.siteName || null, // Use value or null
      recipient_contact: formData.recipientContact || null, // Use value or null
      delivery_route: formData.deliveryRoute || null, // Use value or null
      delivery_dest_abbr: formData.deliveryDestinationAbbreviation || null, // Use value or null
      contact_name: formData.contactName || null, // Use value or null
      pi_percent: formData.pi ? parseFloat(formData.pi) : 0, // Parse PI to float, default to 0
      fixed_rate_adjustment: 0, // Default to 0 if not provided
      shipping_class: null, // Default to null if not provided
      hole_drilling_pattern: null, // Default to null if not provided
      hole_pattern: null, // Default to null if not provided
    };

    // Log the payload to the console for debugging
    console.log("DEBUG: Payload being sent from frontend (with UTC date):", payload);

    try {
      // Send the POST request to the API
      const response = await fetch("/api/create/order", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      // Handle non-OK responses (e.g., 4xx, 5xx)
      if (!response.ok) {
        const errorData = await response.json();
        // Specific error message for foreign key constraint related to customer
        if (errorData.error && errorData.error.includes("foreign key constraint fails") && errorData.error.includes("fk_orders_customer")) {
            throw new Error("The selected customer was not found in the system. Please check again.");
        }
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      // Show success message and navigate
      Swal.fire({
        icon: 'success',
        title: 'Save Successfully!',
        showConfirmButton: true,
        confirmButtonText: 'OK'
      }).then(() => {
        navigate("/import-orders"); // Navigate to orders list after success
      });

    } catch (err) {
      // Handle errors during the API call
      const displayErrorMessage = err.message;
      setSaveError(displayErrorMessage);
      console.error("Error saving order:", err);
      Swal.fire({
        icon: 'error',
        title: 'Save Error!',
        text: displayErrorMessage,
      });
    } finally {
      setIsSaving(false); // Reset saving state
    }
  };

  // Handles the cancel action, navigates back to the orders list
  const handleCancel = () => {
    navigate("/import-orders");
  };

  return (
    <div className="max-w-5xl mx-auto p-6">
      <h1
        data-testid="text-createFormOrder-order"
        className="text-2xl font-bold mb-4"
      >
        Order Add
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-2.5 gap-y-4">
        <div>
          <label
            data-testid="text-createFormOrder-orderDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Order Date
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-createFormOrder-orderDate"
              ref={orderDateRef}
              wrapperClassName="w-full"
              selected={formData.orderDate}
              onChange={(date) => handleDateChange(date, "orderDate")}
              popperPlacement="bottom-start"
              portalId="root-portal"
              inline={false}
              dateFormat="yyyy-MM-dd" // Date format for display: YYYY-MM-DD
              isClearable={true}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.orderDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(orderDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-orderNo"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Order No.
          </label>
          <input
            data-testid="input-createFormOrder-orderNo"
            type="text"
            name="orderNo"
            value={formData.orderNo}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-specifiedDeliveryDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Specified Delivery Date
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-createFormOrder-specifiedDeliveryDate"
              ref={specifiedDeliveryDateRef}
              wrapperClassName="w-full"
              selected={formData.specifiedDeliveryDate}
              onChange={(date) => handleDateChange(date, "specifiedDeliveryDate")}
              dateFormat="yyyy-MM-dd" // Date format for display: YYYY-MM-DD
              isClearable={true}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.specifiedDeliveryDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(specifiedDeliveryDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-customerAbbr"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Customer Abbr.
          </label>
          {fetchCustomersError ? (
            <div className="text-red-500 text-sm">{fetchCustomersError}</div>
          ) : (
            <select
              data-testid="select-createFormOrder-customerAbbr"
              name="customerAbbr"
              value={formData.customerAbbr}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option value="">Select Customer</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.id}>
                  {customer.customer_abbreviation}
                </option>
              ))}
            </select>
          )}
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-siteName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Site Name
          </label>
          <input
            data-testid="input-createFormOrder-siteName"
            type="text"
            name="siteName"
            value={formData.siteName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label className="block text-base font-medium text-gray-700 mb-2">
            Recipient Contact
          </label>
          <input
            type="text"
            name="recipientContact"
            value={formData.recipientContact}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-deliveryRoute"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Delivery Route
          </label>
          <input
            data-testid="input-createFormOrder-deliveryRoute"
            type="text"
            name="deliveryRoute"
            value={formData.deliveryRoute}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-deliveryDestinationAbbreviation"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Delivery Destination Abbreviation
          </label>
          <input
            data-testid="input-createFormOrder-deliveryDestinationAbbreviation"
            type="text"
            name="deliveryDestinationAbbreviation"
            value={formData.deliveryDestinationAbbreviation}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-contactName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Contact Name
          </label>
          <input
            data-testid="input-createFormOrder-contactName"
            type="text"
            name="contactName"
            value={formData.contactName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-rank"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Rank
          </label>
          {fetchRanksError ? (
            <div className="text-red-500 text-sm">{fetchRanksError}</div>
          ) : (
            <select
              data-testid="select-createFormOrder-rank"
              name="rank"
              value={formData.rank}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option value="">Select Rank</option>
              {ranks.map((rankItem) => (
                <option key={rankItem.id} value={rankItem.id}>
                  {rankItem.rank_name}
                </option>
              ))}
            </select>
          )}
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-pi"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            PI
          </label>
          <input
            data-testid="input-createFormOrder-pi"
            type="number"
            name="pi"
            value={formData.pi}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {isSaving && (
        <div className="text-center py-2 text-blue-600">
          Saving...
        </div>
      )}
      {saveError && (
        <div className="text-center py-2 text-red-600">
          {saveError}
        </div>
      )}

      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-createFormOrder-save"
          onClick={handleSave}
          disabled={isSaving}
        />
        <CancelButton
          dataTestId="button-createFormOrder-cancel"
          onClick={handleCancel}
          disabled={isSaving}
        />
      </div>
    </div>
  );
}

export default CreateOrder;
