import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";
import CustomSelect from "../../components/CustomSelect";

// React-icons
import { FaPlus, FaTimes } from "react-icons/fa";

function CreateHoleType() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [formData, setFormData] = useState([
    {
      hole_type: "",
      size: "",
      unit: "",
      material_type: "",
      color_type: "",
      pattern: "",
    },
  ]);

  const holeTypeOptions = [
    { value: "Countersink", label: t("holeType.counterSink") },
    { value: "Round hole", label: t("holeType.roundHole") },
    { value: "Slot", label: t("holeType.slot") },
  ];

  const unitOptions = [
    { value: "Φ", label: "Φ" },
    { value: "mm", label: "mm" },
    { value: "cm", label: "cm" },
  ];

  const materialTypeOptions = [
    { value: "Acrylic", label: "Acrylic" },
    { value: "Urethane", label: "Urethane" },
    { value: "Fluorine", label: "Fluorine" },
  ];

  const colorTypeOptions = [
    { value: "Silver", label: "Silver" },
    { value: "Light color", label: "Light color" },
    { value: "Dark color", label: "Dark color" },
  ];

  const handleChange = (index, field, value) => {
    const updatedSets = [...formData];
    updatedSets[index][field] = value;
    setFormData(updatedSets);
  };

  const handleAddSet = () => {
    setFormData([
      ...formData,
      {
        hole_type: "",
        size: "",
        unit: "",
        material_type: "",
        color_type: "",
        pattern: "",
      },
    ]);
  };

  const handleDeleteSet = (index) => {
    const updatedSets = [...formData];
    updatedSets.splice(index, 1);
    setFormData(updatedSets);
  };

  const handleSave = async (event) => {
    event.preventDefault();

    for (const item of formData) {
      if (
        !item.hole_type.trim() ||
        !item.size.trim() ||
        !item.unit.trim() ||
        !item.material_type.trim() ||
        !item.color_type.trim() ||
        !item.pattern.trim()
      ) {
        await Swal.fire({
          icon: "warning",
          title: "Please fill in all required fields.",
          confirmButtonText: "OK",
        });
        return;
      }
    }

    try {
      const response = await axios.post("/api/create/holetype", formData);

      if (response.status === 200 || response.status === 201) {
        await Swal.fire({
          icon: "success",
          title: t("Save successful!"),
          showConfirmButton: false,
          timer: 1500,
        });
        navigate("/hole-type");
      } else {
        await Swal.fire({
          icon: "error",
          title: t("Save failed"),
          text: response.statusText || "",
          confirmButtonText: "OK",
        });
      }
    } catch (error) {
      console.error("Error saving hole types:", error);
      await Swal.fire({
        icon: "error",
        title: t("Save failed due to network error."),
        text: error.message,
        confirmButtonText: "OK",
      });
    }
  };

  const handleCancel = () => {
    navigate("/hole-type");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-holeType-create"
        className="text-2xl font-bold mb-6"
      >
        {t("holeType.holeAdd")}
      </h1>

      {formData.map((set, index) => (
        <div
          key={index}
          className="mb-8 p-6 border border-gray-300 rounded-md shadow-sm relative"
        >
          {/* Delete button - only show if there's more than one form */}
          {formData.length > 1 && (
            <button
              data-testid="button-createFormHoleType-deleteForm"
              name="buttonDeleteFormHoleType"
              type="button"
              onClick={() => handleDeleteSet(index)}
              className="absolute top-2 right-2 text-red-500 hover:text-red-700"
            >
              <FaTimes size={20} />
            </button>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            {/* Hole Type */}
            <div>
              <label
                data-testid="text-createFormHoleType-holeType"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.hole")}
              </label>
              <CustomSelect
                data-testid="select-createFormHoleType-holeType"
                name="hole_type"
                value={set.hole_type}
                options={holeTypeOptions}
                onChange={(e) =>
                  handleChange(index, "hole_type", e.target.value)
                }
              />
            </div>

            {/* Size */}
            <div>
              <label
                data-testid="text-createFormHoleType-size"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.size")}
              </label>
              <input
                data-testid="input-createFormHoleType-size"
                name="size"
                type="number"
                value={set.size}
                onChange={(e) => handleChange(index, "size", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Unit */}
            <div>
              <label
                data-testid="text-createFormHoleType-unit"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.unit")}
              </label>
              <CustomSelect
                data-testid="select-createFormHoleType-unit"
                name="unit"
                value={set.unit}
                options={unitOptions}
                onChange={(e) => handleChange(index, "unit", e.target.value)}
              />
            </div>

            {/* Material Type */}
            <div>
              <label
                data-testid="text-createFormHoleType-materialType"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.materialType")}
              </label>
              <CustomSelect
                data-testid="select-createFormHoleType-materialType"
                name="material_type"
                value={set.material_type}
                options={materialTypeOptions}
                onChange={(e) =>
                  handleChange(index, "material_type", e.target.value)
                }
              />
            </div>

            {/* Color Types */}
            <div>
              <label
                data-testid="text-createFormHoleType-colorTypes"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.colorTypes")}
              </label>
              <CustomSelect
                data-testid="select-createFormHoleType-colorTypes"
                name="color_type"
                value={set.color_type}
                options={colorTypeOptions}
                onChange={(e) =>
                  handleChange(index, "color_type", e.target.value)
                }
              />
            </div>

            {/* Pattern */}
            <div>
              <label
                data-testid="text-createFormHoleType-pattern"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.pattern")}
              </label>
              <input
                data-testid="input-createFormHoleType-pattern"
                name="pattern"
                type="text"
                value={set.pattern}
                onChange={(e) => handleChange(index, "pattern", e.target.value)}
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      ))}

      {/* Add button */}
      <div className="flex justify-end mb-8">
        <button
          data-testid="button-createFormHoleType-addForm"
          name="buttonAddFormHoleType"
          type="button"
          onClick={handleAddSet}
          className="w-12 h-12 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center text-white shadow-md transition-colors duration-200"
        >
          <FaPlus size={24} />
        </button>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-createFormHoleType-saveHoleType"
          onClick={handleSave}
        />
        <CancelButton
          dataTestId="button-createFormHoleType-cancelHoleType"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default CreateHoleType;
