import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Swal from 'sweetalert2';
import Footer from "../../components/Footer";
import Editbutton from "../../components/EditButton";
import Deletebutton from "../../components/DeleteButton";
// Import your custom Savebutton and Canclebutton components
import Savebutton from "../../components/SaveButton"; // Assuming this path is correct
import Canclebutton from "../../components/CancelButton"; // Assuming this path is correct

function MaterialsAdd() {
  const [currentPage, setCurrentPage] = useState(0);
  const navigate = useNavigate();
  const { t } = useTranslation();

  // State for form fields, initialized to empty for new entry
  const [materialName, setMaterialName] = useState("");
  const [maxLength, setMaxLength] = useState("");
  const [thickness, setThickness] = useState("");
  const [shapeType, setShapeType] = useState("");
  const [price, setPrice] = useState("");

  // State for saving process and messages
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // State for the table data
  const [tableData, setTableData] = useState([]);
  const [loadingTableData, setLoadingTableData] = useState(true);
  const [tableDataError, setTableDataError] = useState(null);

  const handleCancel = () => {
    navigate("/material");
  };

  // Handler for Save button, sends data to backend to create new material
  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    const materialData = {
      material_name: materialName,
      maximum_length: maxLength,
      thickness: thickness,
      shape_type: shapeType,
      price: price,
    };

    const requiredFields = [
      { value: materialName, label: t("material.materialName") },
      { value: maxLength, label: t("material.maxLen") },
      { value: thickness, label: t("material.thickness") },
      { value: shapeType, label: t("material.curObj") },
      { value: price, label: t("material.matetialPrice") },
    ];

    const missingFields = [];
    requiredFields.forEach(field => {
      if (typeof field.value === 'string' && field.value.trim() === '') {
        missingFields.push(field.label);
      } else if (field.value === null || field.value === undefined) {
        missingFields.push(field.label);
      }
    });

    if (missingFields.length > 0) {
      const errorMessage = `${t("action.saveError")}: ${t("validation.missingFields")}: ${missingFields.join(', ')}.`;
      setSaveError(errorMessage);
      Swal.fire({
        icon: 'error',
        title: t("validation.validationError"),
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    try {
      const response = await fetch("/api/create/material", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(materialData),
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("text/html")) {
          const errorText = await response.text();
          console.error("Backend returned HTML error:", errorText);
          throw new Error(`Server error (${response.status}): Please check backend logs. Response was HTML.`);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
      }

      setSaveSuccess(true);
      Swal.fire({
        icon: 'success',
        title: 'Save Successfully!',
        showConfirmButton: true,
        confirmButtonText: 'OK'
      }).then(() => {
        navigate("/material");
      });

    } catch (err) {
      const displayErrorMessage = err.message;
      setSaveError(displayErrorMessage);
      console.error("Error saving material:", err);
      Swal.fire({
        icon: 'error',
        title: t("action.saveError"),
        text: displayErrorMessage,
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Function to fetch materials for the table
  const fetchMaterials = async () => {
    setLoadingTableData(true);
    setTableDataError(null);
    try {
      const response = await fetch("/api/fetch/material");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setTableData(data);
    } catch (err) {
      setTableDataError(err);
      console.error("Error fetching materials for table:", err);
    } finally {
      setLoadingTableData(false);
    }
  };

  // Fetch materials when the component mounts
  useEffect(() => {
    fetchMaterials();
  }, []);

  const handleTableEditClick = (id) => {
    const materialToEdit = tableData.find((item) => item.id === id);
    navigate(`/materialedit/${id}`, { state: { materialToEdit } });
  };

  const handleTableDeleteClick = async (id) => {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await fetch("/api/delete/material", {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ id }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
          }

          Swal.fire(
            'Deleted!',
            'Your material has been deleted.',
            'success'
          );
          // Re-fetch materials to update the list after deletion
          fetchMaterials();
        } catch (err) {
          console.error("Error deleting material:", err);
          Swal.fire(
            'Error!',
            `Failed to delete material: ${err.message}`,
            'error'
          );
        }
      }
    });
  };

  // Pagination logic for the table
  const paginatedData = tableData.slice(currentPage * 10, (currentPage + 1) * 10);
  const totalPages = Math.ceil(tableData.length / 10);

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1
          data-testid="text-addFormMaterial-create"
          name="textAddFormMaterialcreate"
          className="text-2xl font-bold mb-6"
        >
          {t("material.materialAdd")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          {/* Using custom Savebutton component */}
          <Savebutton
            dataTestId="button-addFormMaterial-save"
            name="buttonAddFormMaterialSave"
            onClick={handleSave}
            disabled={isSaving} // Pass disabled prop to the custom button
          />
          {/* Using custom Canclebutton component */}
          <Canclebutton
            dataTestId="button-addFormMaterial-cancel"
            name="buttonAddFormMaterialCancel"
            onClick={handleCancel}
            disabled={isSaving} // Pass disabled prop to the custom button
          />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-addFormMaterial-name"
                name="textAddFormMaterialname"
                className="block font-bold mb-1"
              >
                {t("material.materialName")}
              </label>
              <input
                data-testid="input-addFormMaterial-name"
                name="inputAddFormMaterialname"
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={materialName}
                onChange={(e) => setMaterialName(e.target.value)}
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-addFormMaterial-shape"
                name="textAddFormMaterialshape"
                className="block font-bold mb-1"
              >
                {t("material.curObj")}
              </label>
              <select
                data-testid="select-addFormMaterial-shape"
                name="selectAddFormMaterialshape"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={shapeType}
                onChange={(e) => setShapeType(e.target.value)}
              >
                <option value="">Select a shape</option>
                <option value="Round">Round</option>
                <option value="Square">Square</option>
                <option value="Rectangle">Rectangle</option>
              </select>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-addFormMaterial-length"
                name="textAddFormMateriallength"
                className="block font-bold mb-1"
              >
                {t("material.maxLen")}
              </label>
              <input
                data-testid="input-addFormMaterial-length"
                name="inputAddFormMateriallength"
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={maxLength}
                onChange={(e) => setMaxLength(e.target.value)}
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-addFormMaterial-price"
                name="textAddFormMaterialprice"
                className="block font-bold mb-1"
              >
                {t("material.matetialPrice")}
              </label>
              <input
                data-testid="input-addFormMaterial-price"
                name="inputAddFormMaterialprice"
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
              />
            </div>
          </div>

          <div className="flex flex-col items-start gap-2">
            <label
              data-testid="text-addFormMaterial-thicknesses"
              name="textAddFormMaterialthicknesses"
              className="block font-bold "
            >
              {t("material.thickness")}
            </label>
            <input
              data-testid="input-addFormMaterial-thicknesses"
              name="inputAddFormMaterialthicknesses"
              type="number"
              className="w-full md:w-96 border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              value={thickness}
              onChange={(e) => setThickness(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* --- Table Section with Scrollability --- */}
      <div className="overflow-x-auto mt-5">
        <div className="max-h-80 overflow-y-auto border border-gray-300 rounded-md shadow-md">
          <table
            data-testid="table-addFormMaterial"
            name="tableAddFormMaterial"
            className="w-full bg-white border-collapse whitespace-nowrap text-sm"
          >
            <thead className="bg-[#4472C4] text-white sticky top-0 z-10">
              <tr>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.id")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.materialName")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.maxLen")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.thickness")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.curObj")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.matetialPrice")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.operation")}
                </th>
              </tr>
            </thead>
            <tbody>
              {loadingTableData ? (
                <tr>
                  <td colSpan="7" className="text-center py-4 text-gray-500">
                    {t("material.loadingData")}
                  </td>
                </tr>
              ) : tableDataError ? (
                <tr>
                  <td colSpan="7" className="text-center py-4 text-red-600">
                    {t("material.errorLoadingData")}: {tableDataError.message}
                  </td>
                </tr>
              ) : paginatedData.length > 0 ? (
                paginatedData.map((item, index) => (
                  <tr
                    key={item.id || index}
                    className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                  >
                    <td className="border border-gray-300 px-4 py-1">
                      {item.id}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.material_name}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.maximum_length}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.thickness}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.shape_type}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.price}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      <div className="flex justify-center space-x-2">
                        <Editbutton
                          dataTestId={`button-table-edit-${item.id}`}
                          name={`buttonTableEdit${item.id}`}
                          onClick={() => handleTableEditClick(item.id)}
                        />
                        <Deletebutton
                          dataTestId={`button-table-delete-${item.id}`}
                          name={`buttonTableDelete${item.id}`}
                          onClick={() => handleTableDeleteClick(item.id)}
                        />
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="text-center py-4 text-gray-500">
                    {t("material.noDataFound")}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      {/* --- End Table Section --- */}

      {/* Footer for pagination */}
      <Footer
        className="fixed table-footer-group"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={tableData.length}
      />
    </div>
  );
}

export default MaterialsAdd;