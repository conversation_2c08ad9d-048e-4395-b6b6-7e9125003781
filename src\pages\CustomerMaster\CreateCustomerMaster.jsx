import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import Components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";
import CustomSelect from "../../components/CustomSelect";

function CreateCustomerMaster() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [formData, setFormData] = useState({
    order_no: "",
    fixed_rate_adjustment: "",
    customer_abbreviation: "",
    site_name: "",
    recipient_contact: "",
    contact_person: "",
    contact_email: "",
    delivery_destination: "",
    shipping_class: "",
    hole_drilling_pattern: "",
    processing_pattern: "",
    hole_pattern: "",
    rank: "",
  });

  const [rankOptions, setRankOptions] = useState([]);

  const rankSelectOptions = rankOptions.map((rank) => ({
    value: rank.rank_name,
    label: rank.rank_name,
  }));

  const shippingClassOptions = [
    { value: "class1", label: "Class 1" },
    { value: "class2", label: "Class 2" },
    { value: "class3", label: "Class 3" },
  ];

  const holeDrillingPatternOptions = [
    { value: "cost1", label: "Cost 1" },
    { value: "cost2", label: "Cost 2" },
    { value: "cost3", label: "Cost 3" },
  ];

  const processingPatternOptions = [
    { value: "pattern1", label: "Pattern 1" },
    { value: "pattern2", label: "Pattern 2" },
    { value: "pattern3", label: "Pattern 3" },
  ];

  const holePatternOptions = [
    { value: "pattern1", label: "Pattern 1" },
    { value: "pattern2", label: "Pattern 2" },
    { value: "pattern3", label: "Pattern 3" },
  ];

  useEffect(() => {
    const fetchRanks = async () => {
      try {
        const response = await axios.get("/api/fetch/rank");
        if (response.data) {
          setRankOptions(response.data);
        }
      } catch (error) {
        console.error("Error fetching ranks:", error);
      }
    };

    fetchRanks();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = async (event) => {
    event.preventDefault();

    const {
      order_no,
      fixed_rate_adjustment,
      customer_abbreviation,
      site_name,
      recipient_contact,
      contact_email,
      delivery_destination,
      shipping_class,
      hole_drilling_pattern,
      processing_pattern,
      hole_pattern,
      rank,
    } = formData;

    if (
      !order_no.trim() ||
      !fixed_rate_adjustment.toString().trim() ||
      !customer_abbreviation.trim() ||
      !site_name.trim() ||
      !recipient_contact.trim() ||
      !contact_email.trim() ||
      !delivery_destination.trim() ||
      !shipping_class.trim() ||
      !hole_drilling_pattern.trim() ||
      !processing_pattern.trim() ||
      !hole_pattern.trim() ||
      !rank.trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        text: "All fields are mandatory.",
        confirmButtonText: "OK",
      });
      return;
    }

    // 🔁 แปลงก่อนส่ง
    const formDataToSend = {
      ...formData,
      fixed_rate_adjustment: formData.fixed_rate_adjustment
        ? parseFloat(formData.fixed_rate_adjustment)
        : null,
      recipient_contact: formData.recipient_contact || null,
      delivery_destination: formData.delivery_destination || null,
      shipping_class: formData.shipping_class || null,
      hole_drilling_pattern: formData.hole_drilling_pattern || null,
      processing_pattern: formData.processing_pattern || null,
      hole_pattern: formData.hole_pattern || null,
      rank: formData.rank || null,
    };

    try {
      const response = await axios.post("/api/create/customer", formDataToSend);

      if (response.status === 201 || response.status === 200) {
        Swal.fire({
          icon: "success",
          title: "Saved successfully!",
          showConfirmButton: false,
          timer: 1500,
        });

        navigate("/customer-master");
      }
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "An error occurred while saving.",
        text: error.response?.data?.error || "Unable to save the customer.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/customer-master");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-customer-create"
        className="text-lg sm:text-xl md:text-2xl font-bold mb-5"
      >
        {t("customer.customerManagement")}
      </h1>

      <form onSubmit={handleSave}>
        <div className="grid grid-cols-2 gap-x-6 gap-y-2">
          {/* First Row */}
          <div>
            <label
              data-testid="text-createFormCustomer-orderNo"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.orderNo")}
            </label>
            <input
              data-testid="input-createFormCustomer-orderNo"
              type="text"
              name="order_no"
              value={formData.order_no}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label
              data-testid="text-createFormCustomer-fixedRate"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.fixedRate")}
            </label>
            <div className="relative w-full">
              <input
                data-testid="input-createFormCustomer-fixedRate"
                type="number"
                name="fixed_rate_adjustment"
                value={formData.fixed_rate_adjustment}
                onChange={handleChange}
                className="w-full px-4 py-2 pr-10 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="absolute inset-y-0 right-3 flex items-center text-gray-700 text-base pointer-events-none">
                %
              </span>
            </div>
          </div>

          {/* Second Row */}
          <div>
            <label
              data-testid="text-createFormCustomer-customerAbbr"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.customerAbbr")}
            </label>
            <input
              data-testid="input-createFormCustomer-customerAbbr"
              type="text"
              name="customer_abbreviation"
              value={formData.customer_abbreviation}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label
              data-testid="text-createFormCustomer-siteName"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.siteName")}
            </label>
            <input
              data-testid="input-createFormCustomer-siteName"
              type="text"
              name="site_name"
              value={formData.site_name}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Third Row - Full Width */}
          <div>
            <label
              data-testid="text-createFormCustomer-contactPerson"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.person")}
            </label>
            <input
              data-testid="input-createFormCustomer-contactPerson"
              type="text"
              name="contact_person"
              value={formData.contact_person}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label
              data-testid="text-createFormCustomer-email"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.email")}
            </label>
            <input
              data-testid="input-createFormCustomer-email"
              type="email"
              name="contact_email"
              value={formData.contact_email}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Fourth Row */}
          <div>
            <label
              data-testid="text-createFormCustomer-deliveryDestination"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.deliveryDestination")}
            </label>
            <input
              data-testid="input-createFormCustomer-deliveryDestination"
              type="text"
              name="delivery_destination"
              value={formData.delivery_destination}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label
              data-testid="text-createFormCustomer-shippingClass"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.shippingClass")}
            </label>
            <CustomSelect
              data-testid="select-createFormCustomer-shippingClass"
              name="shipping_class"
              value={formData.shipping_class}
              onChange={handleChange}
              options={shippingClassOptions}
            />
          </div>

          {/* Fifth Row */}
          <div>
            <label
              data-testid="text-createFormCustomer-holeDrillingPattern"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.holeDrillingPattern")}
            </label>
            <CustomSelect
              data-testid="select-createFormCustomer-holeDrillingPattern"
              name="hole_drilling_pattern"
              value={formData.hole_drilling_pattern}
              onChange={handleChange}
              options={holeDrillingPatternOptions}
            />
          </div>

          <div>
            <label
              data-testid="text-createFormCustomer-processingPattern"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.processingPattern")}
            </label>
            <CustomSelect
              data-testid="select-createFormCustomer-processingPattern"
              name="processing_pattern"
              value={formData.processing_pattern}
              onChange={handleChange}
              options={processingPatternOptions}
            />
          </div>

          {/* Sixth Row */}
          <div>
            <label
              data-testid="text-createFormCustomer-holePattern"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.holePattern")}
            </label>
            <CustomSelect
              data-testid="select-createFormCustomer-holePattern"
              name="hole_pattern"
              value={formData.hole_pattern}
              onChange={handleChange}
              options={holePatternOptions}
            />
          </div>

          <div>
            <label
              data-testid="text-createFormCustomer-rank"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.rank")}
            </label>
            <CustomSelect
              data-testid="select-createFormCustomer-rank"
              name="rank"
              value={formData.rank}
              options={rankSelectOptions}
              onChange={handleChange}
              menuPlacement="top" 
            />
          </div>

          {/* Seventh Row */}
          <div>
            <label
              data-testid="text-createFormCustomer-recipientContact"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("customer.recipientContact")}
            </label>
            <input
              data-testid="input-createFormCustomer-recipientContact"
              type="text"
              name="recipient_contact"
              value={formData.recipient_contact}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div></div>
        </div>

        {/* Button Group */}
        <div className="flex gap-4 mt-8">
          <SaveButton dataTestId="button-createFormCustomer-save" />
          <CancelButton
            dataTestId="button-createFormCustomer-cancel"
            onClick={handleCancel}
          />
        </div>
      </form>
    </div>
  );
}

export default CreateCustomerMaster;
