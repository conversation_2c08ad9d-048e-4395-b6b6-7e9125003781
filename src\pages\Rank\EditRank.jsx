import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

function EditRank() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const { rankToEdit } = location.state || {};

  const [rankForm, setRankForm] = useState({
    rank_name: "",
    selling_price: "",
    rate_percent: "",
    curvedObject: "",
    cuttingBoard: "",
  });

  const [id, setId] = useState(null);
  // Load data when component mounts
  useEffect(() => {
    if (rankToEdit) {
      setRankForm({
        rank_name: rankToEdit.rank_name || "",
        selling_price: rankToEdit.selling_price || "",
        rate_percent: rankToEdit.rate_percent || "",
        curvedObject: rankToEdit.curvedObject || "",
        cuttingBoard: rankToEdit.cuttingBoard || "",
      });
      setId(rankToEdit.id || null);
    }
  }, [rankToEdit]);

  const handleChange = (field, value) => {
    setRankForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleUpdate = async () => {
    const {
      rank_name,
      selling_price,
      rate_percent,
      curvedObject,
      cuttingBoard,
    } = rankForm;

    if (
      !rank_name.trim() ||
      !selling_price.toString().trim() ||
      !rate_percent.toString().trim() ||
      !curvedObject.toString().trim() ||
      !cuttingBoard.toString().trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const payload = {
        id,
        rank_name,
        selling_price,
        rate_percent,
        curvedObject,
        cuttingBoard,
      };

      const response = await axios.put("/api/edit/rank", payload);

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/rank");
      }
    } catch (error) {
      console.error("Error updating rank:", error);
      Swal.fire({
        icon: "error",
        title: "Error updating data",
        text: error.response?.data?.error || "Unable to update the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/rank");
  };

  return (
    <div className="max-w-5xl mx-auto p-3">
      <h1
        data-testid="text-editFormRank-rank"
        className="text-2xl font-bold mb-6"
      >
        {t("createAndEditRank.rankEdit")}
      </h1>

      <div className="mb-4">
        <div className="flex flex-row items-center gap-2">
          <div className="flex flex-col flex-[1] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.rank")}
            </label>
            {/* Rank input */}
            <input
              data-testid="input-editFormRank-rank"
              name="rank_name"
              type="text"
              value={rankForm.rank_name}
              onChange={(e) => handleChange("rank_name", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3 text-center"
            />
          </div>

          {/* Selling Price input */}
          <div className="flex flex-col flex-[3] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.sellingPrice")}
            </label>
            <input
              data-testid="input-editFormRank-sellingPrice"
              name="selling_price"
              type="number"
              value={rankForm.selling_price}
              onChange={(e) => handleChange("selling_price", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* Rate input */}
          <div className="flex flex-col flex-[2] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.ratePercent")}
            </label>
            <input
              data-testid="input-editFormRank-rate"
              name="rate_percent"
              type="number"
              value={rankForm.rate_percent}
              onChange={(e) => handleChange("rate_percent", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* curvedObject input */}
          <div className="flex flex-col flex-[2] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.curvedObject")}
            </label>
            <input
              data-testid="input-editFormRank-curvedObject"
              name="curvedObject"
              type="number"
              value={rankForm.curvedObject}
              onChange={(e) => handleChange("curvedObject", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* Cutting Board input */}
          <div className="flex flex-col flex-[2] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.cuttingBoard")}
            </label>
            <input
              data-testid="input-editFormRank-cuttingBoard"
              name="cuttingBoard"
              type="number"
              value={rankForm.cuttingBoard}
              onChange={(e) => handleChange("cuttingBoard", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* Required indicator */}
          <div className="pt-6">
            <span className="text-xl font-bold">*</span>
          </div>
        </div>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          data-testid="button-editFormRank-saveRank"
          onClick={handleUpdate}
        />
        <CancelButton
          data-testid="button-editFormRank-cancelRank"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default EditRank;
