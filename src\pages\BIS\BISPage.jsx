import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";

function BISPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [currentPage, setCurrentPage] = useState(0);
  const [filteredBIS, setFilteredBIS] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBIS = async () => {
      try {
        const response = await axios.get("/api/fetch/bis");
        setFilteredBIS(response.data);
      } catch (error) {
        console.error("Failed to fetch BIS data:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchBIS();
  }, []);

  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredBIS.length / itemsPerPage);
  const paginatedBIS = filteredBIS.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateBIS = () => {
    navigate("/create-bis");
  };

  const handleEditClick = (id) => {
    const bisToEdit = filteredBIS.find((item) => item.id === id);

    if (!bisToEdit) {
      Swal.fire("Not found", "BIS data not found.", "warning");
      return;
    }

    navigate(`/edit-bis/${id}`, { state: { bisToEdit } });
  };

  const handleDeleteClick = async (id) => {
    const confirm = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this item?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (confirm.isConfirmed) {
      try {
        const response = await axios.delete("/api/delete/bis", {
          data: { id },
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Your data has been deleted.",
            showConfirmButton: false,
            timer: 1000,
          });

          // ลบข้อมูลออกจาก state
          setFilteredBIS((prev) => prev.filter((item) => item.id !== id));
        }
      } catch (error) {
        console.error("Error deleting BIS data:", error);
        Swal.fire("Error", "Failed to delete item.", "error");
      }
    }
  };

  return (
    <div className="w-full">
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-bis"
          name="textBis"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("bis.bis")}
        </h1>
        <button
          data-testid="button-createForm-bis"
          name="createBis"
          type="button"
          onClick={handleCreateBIS}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      <div className="overflow-x-auto">
        <table
          data-testid="table-bis"
          name="tableBis"
          className="w-full bg-white border-collapse"
        >
          <thead className="bg-[#4472C4] text-white text-base">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[50px]">
                {t("bis.No")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("bis.screwType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("bis.color")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("bis.price")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-center min-w-[120px]">
                {t("bis.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              [...Array(itemsPerPage)].map((_, i) => (
                <tr
                  key={i}
                  className={i % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  {[...Array(6)].map((_, j) => (
                    <td key={j} className="py-2 px-4 border border-white">
                      <div className="h-4 bg-[#E9EDF9] rounded animate-pulse w-full"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : paginatedBIS.length > 0 ? (
              paginatedBIS.map((item, index) => (
                <tr
                  key={item.id}
                  className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                >
                  <td className="border border-white px-4 py-1">{item.id}</td>
                  <td className="border border-white px-4 py-1">
                    {item.screw_type}
                  </td>
                  <td className="border border-white px-4 py-1">
                    {item.color}
                  </td>
                  <td className="border border-white px-4 py-1">
                    {item.price}
                  </td>
                  <td className="border border-white px-4 py-1">
                    <div className="flex justify-center gap-2">
                      <EditButton
                        dataTestId="button-editForm-bis"
                        onClick={() => handleEditClick(item.id)}
                      />
                      <DeleteButton
                        dataTestId="button-delete-bis"
                        onClick={() => handleDeleteClick(item.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredBIS.length}
      />
    </div>
  );
}

export default BISPage;
