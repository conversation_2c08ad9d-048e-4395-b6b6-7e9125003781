import { useEffect, useState } from "react";
import Editbutton from "../../components/EditButton";
import Deletebutton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Swal from 'sweetalert2'; // Import SweetAlert2

function Materials() {
  const [currentPage, setCurrentPage] = useState(0);
  const [searchInput, setSearchInput] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [materials, setMaterials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleCreate = () => {
    navigate("/materialadd");
  };

  const handleEditClick = (id) => {
    const materialToEdit = materials.find((item) => item.id === id);
    navigate(`/materialedit/${id}`, { state: { materialToEdit } });
  };

  const handleDeleteClick = async (id) => {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33 ',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await fetch("/api/delete/material", {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ id }), // Send ID in the request body
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
          }

          Swal.fire(
            'Deleted!',
            'Your material has been deleted.',
            'success'
          );
          // Re-fetch materials to update the list after deletion
          fetchMaterials();
        } catch (err) {
          console.error("Error deleting material:", err);
          Swal.fire(
            'Error!',
            `Failed to delete material: ${err.message}`,
            'error'
          );
        }
      }
    });
  };

  useEffect(() => {
    if (searchInput.trim() === "") {
      setSearchTerm("");
      setCurrentPage(0);
    }
  }, [searchInput]);

  const handleSearchClick = () => {
    setSearchTerm(searchInput);
    setCurrentPage(0);
  };

  const fetchMaterials = async () => { // Moved fetchMaterials outside useEffect to be callable
    try {
      const response = await fetch("/api/fetch/material");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setMaterials(data);
    } catch (err) {
      setError(err);
      console.error("Error fetching materials:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMaterials();
  }, []);

  const filteredData = materials.filter((item) => {
    const idValue = item.id ? String(item.id).toLowerCase() : "";
    const materialNameValue = item.material_name ? String(item.material_name).toLowerCase() : "";
    const lowerCaseSearchTerm = searchTerm.toLowerCase();

    return (
      idValue.includes(lowerCaseSearchTerm) ||
      materialNameValue.includes(lowerCaseSearchTerm)
    );
  });

  const paginatedData = filteredData.slice(
    currentPage * 10,
    (currentPage + 1) * 10
  );

  const totalPages = Math.ceil(filteredData.length / 10);

  if (loading) {
    return <div className="text-center py-10">loadingData</div>;
  }

  if (error) {
    return <div className="text-center py-10 text-red-600">errorLoadingData: {error.message}</div>;
  }

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("material.materialManagement")}
        </h1>
        <button
          data-testid="button-create-material"
          name="buttonCreateMaterial"
          className="btn-create"
          onClick={handleCreate}
        >
          {t("action.create")}
        </button>
      </div>

      <div className="mt-1 w-full flex gap-4">
        <input
          data-testid="input-material-search"
          name="inputMaterialSearch"
          type="text"
          placeholder="Search ..."
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="w-6/12 p-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />

        <button
          data-testid="button-material-search"
          name="buttonMaterialSearch"
          onClick={handleSearchClick}
          className="btn-search"
        >
        search
        </button>
      </div>

      <div className="mt-3 w-full overflow-x-auto">
        <table
          data-testid="table-material"
          name="tableMaterial"
          className="w-full bg-white border-collapse whitespace-nowrap"
        >
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.id")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.materialName")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.maxLen")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.thickness")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.curObj")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.matetialPrice")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item) => (
              <tr
                key={item.id}
                className={paginatedData.indexOf(item) % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.id}
                </td>
                <td className="border border-white px-4 py-2 w-1/2">
                  {item.material_name}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.maximum_length}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.thickness}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.shape_type}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.price}
                </td>
                <td className="border border-white px-4 py-1">
                  <div className="flex justify-center space-x-2">
                    <Editbutton
                      dataTestId="button-edit-material"
                      name="buttonEditMaterial"
                      onClick={() => handleEditClick(item.id)}
                    />
                    <Deletebutton
                      dataTestId="button-delete-material"
                      name="buttonDeleteMaterial"
                      onClick={() => handleDeleteClick(item.id)} // Connect delete button
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedData.length === 0 && (
              <tr>
                <td colSpan="7" className="text-center py-4 text-gray-500">
                  {t("material.noDataFound")}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Footer
        className="fixed table-footer-group"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredData.length}
      />
    </div>
  );
}

export default Materials;
