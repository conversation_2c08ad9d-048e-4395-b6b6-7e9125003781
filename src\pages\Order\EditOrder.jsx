import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Swal from 'sweetalert2';

import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

import { AiTwotoneCalendar } from "react-icons/ai";

function EditOrder() {
  const location = useLocation();
  const navigate = useNavigate();
  // const { t } = useTranslation(); // Removed useTranslation hook
  const { orderToEdit } = location.state || {};

  const orderDateRef = useRef(null);
  const specifiedDeliveryDateRef = useRef(null);

  const [formData, setFormData] = useState(() => {
    if (orderToEdit) {
      return {
        id: orderToEdit.id,
        orderDate: orderToEdit.orderDate ? new Date(orderToEdit.orderDate) : null,
        orderNo: orderToEdit.order_no || "",
        specifiedDeliveryDate: orderToEdit.specifiedDeliveryDate ? new Date(orderToEdit.specifiedDeliveryDate) : null,
        customerAbbr: orderToEdit.customerAbbr || "",
        siteName: orderToEdit.site_name || "",
        contactName: orderToEdit.contact_name || "",
        recipientContact: orderToEdit.recipient_contact || "",
        deliveryRoute: orderToEdit.delivery_route || "",
        deliveryDestinationAbbreviation: orderToEdit.delivery_dest_abbr || "",
        rank: orderToEdit.rank || "",
        pi: orderToEdit.pi !== undefined && orderToEdit.pi !== null ? orderToEdit.pi : "",
      };
    }
    return {
      id: null,
      orderDate: null,
      orderNo: "",
      specifiedDeliveryDate: null,
      customerAbbr: "",
      siteName: "",
      contactName: "",
      recipientContact: "",
      deliveryRoute: "",
      deliveryDestinationAbbreviation: "",
      rank: "",
      pi: "",
    };
  });

  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [customers, setCustomers] = useState([]);
  const [fetchCustomersError, setFetchCustomersError] = useState(null);
  const [ranks, setRanks] = useState([]);
  const [fetchRanksError, setFetchRanksError] = useState(null);

  useEffect(() => {
    if (!orderToEdit) {
      Swal.fire({
        icon: 'warning',
        title: "No Order Selected", // Changed from t("action.noOrderSelected")
        text: "Please select an order to edit.", // Changed from t("action.pleaseSelectOrderToEdit")
      }).then(() => {
        navigate("/import-orders");
      });
      return;
    }

    const fetchCustomers = async () => {
      try {
        const response = await fetch("/api/fetch/customer");
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setCustomers(data);
      } catch (error) {
        console.error("Error fetching customers:", error);
        setFetchCustomersError(`Error: Error fetching customers - ${error.message}`); // Changed from t("action.error") etc.
        Swal.fire({
          icon: 'error',
          title: "Error", // Changed from t("action.error")
          text: `Error fetching customers: ${error.message}`, // Changed from t("createOrder.errorFetchingCustomers") etc.
        });
      }
    };

    const fetchRanks = async () => {
      try {
        const response = await fetch("/api/fetch/rank");
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setRanks(data);
      } catch (error) {
        console.error("Error fetching ranks:", error);
        setFetchRanksError(`Error: Error fetching ranks - ${error.message}`); // Changed from t("action.error") etc.
        Swal.fire({
          icon: 'error',
          title: "Error", // Changed from t("action.error")
          text: `Error fetching ranks: ${error.message}`, // Changed from t("createOrder.errorFetchingRanks") etc.
        });
      }
    };

    fetchCustomers();
    fetchRanks();
  }, [orderToEdit, navigate]); // Removed t from dependency array

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDateChange = (date, fieldName) => {
    setFormData((prev) => ({
      ...prev,
      [fieldName]: date,
    }));
  };

  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true);
    }
  };

  const formatDateToYYYYMMDD = (date) => {
    if (!date) return null;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);

    if (!formData.id) {
      const errorMessage = "Order ID is missing. Cannot update."; // Changed from t("action.orderIdMissing")
      setSaveError(errorMessage);
      Swal.fire({
        icon: 'error',
        title: "Error", // Changed from t("action.error")
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    const requiredFields = [
      { value: formData.orderNo, label: "Order No." }, // Changed from t("createOrder.orderNo")
      { value: formData.customerAbbr, label: "Customer Abbreviation" }, // Changed from t("createOrder.customerAbb")
      { value: formData.specifiedDeliveryDate, label: "Specified Delivery Date" }, // Changed from t("createOrder.specifiedDeliveryDate")
    ];

    const missingFields = [];
    requiredFields.forEach(field => {
      if (field.value === null || field.value === undefined || (typeof field.value === 'string' && field.value.trim() === '')) {
        missingFields.push(field.label);
      }
    });

    if (missingFields.length > 0) {
      const errorMessage = `Missing required fields: ${missingFields.join(', ')}. Please fill in all required fields.`; // Changed from t("action.missingFields") etc.
      setSaveError(errorMessage);
      Swal.fire({
        icon: 'error',
        title: "Validation Error", // Changed from t("action.validationError")
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    const selectedCustomer = customers.find(c => c.customer_abbreviation === formData.customerAbbr);
    const selectedRank = ranks.find(r => r.rank_name === formData.rank);

    const payload = {
      id: formData.id,
      order_no: formData.orderNo,
      customer_id: selectedCustomer ? selectedCustomer.id : null,
      order_date: formatDateToYYYYMMDD(formData.orderDate),
      delivery_date: formatDateToYYYYMMDD(formData.specifiedDeliveryDate),

      project_name: null,
      material_id: null,
      quantity: 0,
      rank_id: selectedRank ? selectedRank.id : null,
      project_id: null,
      site_name: formData.siteName || null,
      recipient_contact: formData.recipientContact || null,
      delivery_route: formData.deliveryRoute || null,
      delivery_dest_abbr: formData.deliveryDestinationAbbreviation || null,
      contact_name: formData.contactName || null,
      pi_percent: formData.pi ? parseFloat(formData.pi) : 0,
      fixed_rate_adjustment: 0,
      shipping_class: null,
      hole_drilling_pattern: null,
      hole_pattern: null,
    };

    try {
      const response = await fetch(`/api/edit/order`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        let errorMessage = errorData.error || `HTTP error! status: ${response.status}`;

        if (errorData.error && errorData.error.includes("foreign key constraint fails")) {
          if (errorData.error.includes("fk_orders_customer")) {
            errorMessage = "Customer not found. Please select a valid customer."; // Changed from t("action.customerNotFound")
          } else if (errorData.error.includes("fk_orders_rank")) {
            errorMessage = "Rank not found. Please select a valid rank."; // Changed from t("action.rankNotFound")
          }
        }
        throw new Error(errorMessage);
      }

      Swal.fire({
        icon: 'success',
        title: "Order Updated Successfully!", // Changed from t("action.updateSuccess")
        showConfirmButton: true,
        confirmButtonText: 'OK'
      }).then(() => {
        navigate("/import-orders");
      });

    } catch (err) {
      const displayErrorMessage = err.message;
      setSaveError(displayErrorMessage);
      console.error("Error updating order:", err);
      Swal.fire({
        icon: 'error',
        title: "Error Updating Order", // Changed from t("action.updateError")
        text: displayErrorMessage,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    navigate("/import-orders");
  };

  return (
    <div className="max-w-5xl mx-auto p-6">
      <h1
        data-testid="text-editFormOrder-order"
        className="text-2xl font-bold mb-4"
      >
        Edit Order
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-2.5 gap-y-4">
        <div>
          <label
            data-testid="text-editFormOrder-orderDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Order Date
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-editFormOrder-orderDate"
              ref={orderDateRef}
              wrapperClassName="w-full"
              selected={formData.orderDate}
              onChange={(date) => handleDateChange(date, "orderDate")}
              popperPlacement="bottom-start"
              portalId="root-portal"
              inline={false}
              dateFormat="dd/MM/yyyy"
              isClearable={formData.orderDate !== null}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.orderDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(orderDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-orderNo"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Order No.
          </label>
          <input
            data-testid="input-editFormOrder-orderNo"
            type="text"
            name="orderNo"
            value={formData.orderNo}
            readOnly
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 bg-gray-100"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-specifiedDeliveryDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Specified Delivery Date
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-editFormOrder-specifiedDeliveryDate"
              ref={specifiedDeliveryDateRef}
              wrapperClassName="w-full"
              selected={formData.specifiedDeliveryDate}
              onChange={(date) => handleDateChange(date, "specifiedDeliveryDate")}
              dateFormat="dd/MM/yyyy"
              isClearable={formData.specifiedDeliveryDate !== null}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.specifiedDeliveryDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(specifiedDeliveryDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-customerAbbr"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Customer Abbreviation
          </label>
          {fetchCustomersError ? (
            <div className="text-red-500 text-sm">{fetchCustomersError}</div>
          ) : (
            <select
              data-testid="select-editFormOrder-customerAbbr"
              name="customerAbbr"
              value={formData.customerAbbr}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option value="">Select Customer</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.customer_abbreviation}>
                  {customer.customer_abbreviation}
                </option>
              ))}
            </select>
          )}
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-siteName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Site Name
          </label>
          <input
            data-testid="input-editFormOrder-siteName"
            type="text"
            name="siteName"
            value={formData.siteName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-contactName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Contact Name
          </label>
          <input
            data-testid="input-editFormOrder-contactName"
            type="text"
            name="contactName"
            value={formData.contactName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-recipientContact"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Recipient Contact
          </label>
          <input
            data-testid="input-editFormOrder-recipientContact"
            type="text"
            name="recipientContact"
            value={formData.recipientContact}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-deliveryRoute"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Delivery Route
          </label>
          <input
            data-testid="input-editFormOrder-deliveryRoute"
            type="text"
            name="deliveryRoute"
            value={formData.deliveryRoute}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-deliveryDestinationAbbreviation"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Delivery Destination Abbreviation
          </label>
          <input
            data-testid="input-editFormOrder-deliveryDestinationAbbreviation"
            type="text"
            name="deliveryDestinationAbbreviation"
            value={formData.deliveryDestinationAbbreviation}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-rank"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            Rank
          </label>
          {fetchRanksError ? (
            <div className="text-red-500 text-sm">{fetchRanksError}</div>
          ) : (
            <select
              data-testid="select-editFormOrder-rank"
              name="rank"
              value={formData.rank}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option value="">Select Rank</option>
              {ranks.map((rankItem) => (
                <option key={rankItem.id} value={rankItem.rank_name}>
                  {rankItem.rank_name}
                </option>
              ))}
            </select>
          )}
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-pi"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            PI (%)
          </label>
          <input
            data-testid="input-editFormOrder-pi"
            type="number"
            name="pi"
            value={formData.pi}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="md:col-span-1"></div>
      </div>

      {isSaving && (
        <div className="text-center py-2 text-blue-600">
          Saving...
        </div>
      )}
      {saveError && (
        <div className="text-center py-2 text-red-600">
          {saveError}
        </div>
      )}

      <div className="flex justify-start mt-6 gap-2">
        <SaveButton
          dataTestId="button-editFormOrder-save"
          onClick={handleSave}
          disabled={isSaving}
        />
        <CancelButton
          dataTestId="button-editFormOrder-cancel"
          onClick={handleCancel}
          disabled={isSaving}
        />
      </div>
    </div>
  );
}

export default EditOrder;
