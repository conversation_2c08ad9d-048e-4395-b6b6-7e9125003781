import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import axios from "axios";
import Swal from "sweetalert2";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";
import CustomSelect from "../../components/CustomSelect";

function EditHoleType() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const { holeTypeToEdit } = location.state || {};

  const [formData, setFormData] = useState({
    hole_type: "",
    size: "",
    unit: "",
    material_type: "",
    color_type: "",
    pattern: "",
  });

  const holeTypeOptions = [
    { value: "Countersink", label: t("holeType.counterSink") },
    { value: "Round hole", label: t("holeType.roundHole") },
    { value: "Slot", label: t("holeType.slot") },
  ];

  const unitOptions = [
    { value: "Φ", label: "Φ" },
    { value: "mm", label: "mm" },
    { value: "cm", label: "cm" },
  ];

  const materialTypeOptions = [
    { value: "Acrylic", label: "Acrylic" },
    { value: "Urethane", label: "Urethane" },
    { value: "Fluorine", label: "Fluorine" },
  ];

  const colorTypeOptions = [
    { value: "Silver", label: "Silver" },
    { value: "Light color", label: "Light color" },
    { value: "Dark color", label: "Dark color" },
  ];

  const [id, setId] = useState(null);

  // Load data when component mounts
  useEffect(() => {
    if (holeTypeToEdit) {
      setFormData({
        hole_type: holeTypeToEdit.hole_type || "",
        size: holeTypeToEdit.size || "",
        unit: holeTypeToEdit.unit || "",
        material_type: holeTypeToEdit.material_type || "",
        color_type: holeTypeToEdit.color_type || "",
        pattern: holeTypeToEdit.pattern || "",
      });
      setId(holeTypeToEdit.id);
    }
  }, [holeTypeToEdit]);

  const handleChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleUpdate = async () => {
    const { hole_type, size, unit, material_type, color_type, pattern } =
      formData;

    if (
      !hole_type.trim() ||
      !size.trim() ||
      !unit.trim() ||
      !material_type.trim() ||
      !color_type.trim() ||
      !pattern.trim()
    ) {
      Swal.fire({
        icon: "warning",
        title: "Please fill in all required fields.",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const payload = {
        id,
        hole_type,
        size,
        unit,
        material_type,
        color_type,
        pattern,
      };

      const response = await axios.put("/api/edit/holetype", payload);

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: "Updated successfully!",
          showConfirmButton: false,
          timer: 1000,
        });
        navigate("/hole-type");
      }
    } catch (error) {
      console.error("Error updating data:", error);
      Swal.fire({
        icon: "error",
        title: "Error updating data",
        text: error.response?.data?.error || "Unable to update the data.",
      });
    }
  };

  const handleCancel = () => {
    navigate("/hole-type");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1 data-testid="text-holeType-edit" className="text-2xl font-bold mb-6">
        {t("holeType.hole")}
      </h1>

      <div className="mb-8 p-6 border border-gray-300 rounded-md shadow-sm relative">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
          {/* Hole Type */}
          <div>
            <label
              data-testid="text-editFormHoleType-holeType"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.hole")}
            </label>
            <CustomSelect
              data-testid="select-createFormHoleType-holeType"
              name="hole_type"
              value={formData.hole_type}
              options={holeTypeOptions}
              onChange={(e) => handleChange("hole_type", e.target.value)}
            />
          </div>

          {/* Size */}
          <div>
            <label
              data-testid="text-editFormHoleType-size"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.size")}
            </label>
            <input
              data-testid="input-editFormHoleType-size"
              name="size"
              type="number"
              value={formData.size}
              onChange={(e) => handleChange("size", e.target.value)}
              placeholder="0"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Unit */}
          <div>
            <label
              data-testid="text-editFormHoleType-unit"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.unit")}
            </label>
            <CustomSelect
              data-testid="select-createFormHoleType-unit"
              name="unit"
              value={formData.unit}
              options={unitOptions}
              onChange={(e) => handleChange("unit", e.target.value)}
            />
          </div>

          {/* Material Type */}
          <div>
            <label
              data-testid="text-editFormHoleType-materialType"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.materialType")}
            </label>
            <CustomSelect
              data-testid="select-createFormHoleType-materialType"
              name="material_type"
              value={formData.material_type}
              options={materialTypeOptions}
              onChange={(e) => handleChange("material_type", e.target.value)}
            />
          </div>

          {/* Color Type */}
          <div>
            <label
              data-testid="text-editFormHoleType-colorTypes"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.colorTypes")}
            </label>
            <CustomSelect
              data-testid="select-createFormHoleType-colorTypes"
              name="color_type"
              value={formData.color_type}
              options={colorTypeOptions}
              onChange={(e) => handleChange("color_type", e.target.value)}
            />
          </div>

          {/* Pattern */}
          <div>
            <label
              data-testid="text-editFormHoleType-pattern"
              className="block text-base font-medium text-gray-700 mb-2"
            >
              {t("holeType.pattern")}
            </label>
            <input
              data-testid="input-editFormHoleType-pattern"
              name="pattern"
              type="text"
              value={formData.pattern}
              onChange={(e) => handleChange("pattern", e.target.value)}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-editFormHoleType-saveHoleType"
          onClick={handleUpdate}
        />
        <CancelButton
          dataTestId="button-editFormHoleType-cancelHoleType"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default EditHoleType;
