import React, { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Swal from 'sweetalert2';
import Footer from "../../components/Footer";
import Editbutton from "../../components/EditButton";
import Deletebutton from "../../components/DeleteButton";
import Savebutton from "../../components/SaveButton"; // Import custom Savebutton
import Canclebutton from "../../components/CancelButton"; // Import custom Canclebutton

function MaterialsEdit() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { id } = useParams(); // Get ID from URL parameters
  const location = useLocation();
  const materialToEditFromState = location.state?.materialToEdit; // Get pre-fetched data from state

  // State for form fields
  const [currentMaterialId, setCurrentMaterialId] = useState(id ? parseInt(id) : null);
  const [materialName, setMaterialName] = useState("");
  const [maxLength, setMaxLength] = useState("");
  const [thickness, setThickness] = useState("");
  const [shapeType, setShapeType] = useState("");
  const [price, setPrice] = useState("");

  // State for loading and error messages
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // State for the table data
  const [tableData, setTableData] = useState([]);
  const [loadingTableData, setLoadingTableData] = useState(true);
  const [tableDataError, setTableDataError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);

  // Function to fetch a single material's data for editing
  const fetchMaterialData = async (materialId) => {
    setIsLoading(true);
    setFetchError(null);
    try {
      const response = await fetch(`/api/fetch/material/${materialId}`);
      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("text/html")) {
          const errorText = await response.text();
          console.error("Backend returned HTML error during fetch:", errorText);
          throw new Error(`Server error (${response.status}): Please check backend logs. Response was HTML.`);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
      }
      const data = await response.json();
      setCurrentMaterialId(data.id);
      setMaterialName(data.material_name || "");
      setMaxLength(data.maximum_length || "");
      setThickness(data.thickness || "");
      setShapeType(data.shape_type || "");
      setPrice(data.price || "");
    } catch (err) {
      console.error("Error fetching material data:", err);
      setFetchError(`Failed to load material data: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch all materials for the table
  const fetchAllMaterials = async () => {
    setLoadingTableData(true);
    setTableDataError(null);
    try {
      const response = await fetch("/api/fetch/material"); // Assuming this endpoint fetches all materials
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setTableData(data);
    } catch (err) {
      setTableDataError(err);
      console.error("Error fetching materials for table:", err);
    } finally {
      setLoadingTableData(false);
    }
  };

  useEffect(() => {
    // If an ID is present in the URL, try to fetch the specific material for editing
    if (id) {
      if (materialToEditFromState && materialToEditFromState.id === parseInt(id)) {
        // Prioritize data from location.state if available (means it came from the list page)
        setCurrentMaterialId(materialToEditFromState.id);
        setMaterialName(materialToEditFromState.material_name || "");
        setMaxLength(materialToEditFromState.maximum_length || "");
        setThickness(materialToEditFromState.thickness || "");
        setShapeType(materialToEditFromState.shape_type || "");
        setPrice(materialToEditFromState.price || "");
        setIsLoading(false);
        setFetchError(null);
      } else {
        // If no data in state or ID mismatch, fetch from API
        fetchMaterialData(id);
      }
    } else {
      // If no ID in URL (e.g., accessed directly without an ID), don't load specific material
      setIsLoading(false);
      setFetchError("Material ID is missing from URL. Please select a material from the table below to edit.");
    }

    // Always fetch all materials for the table
    fetchAllMaterials();
  }, [id, materialToEditFromState, t]);


  const handleCancel = () => {
    navigate("/material"); // Navigate back to the materials list
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    if (!currentMaterialId) { // Use currentMaterialId from state
      const errorMessage = "Cannot save: No material selected for editing.";
      setSaveError(errorMessage);
      Swal.fire({
        icon: 'error',
        title: 'Save Error',
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    const materialData = {
      id: currentMaterialId, // Add the ID to the request body
      material_name: materialName,
      maximum_length: maxLength,
      thickness: thickness,
      shape_type: shapeType,
      price: price,
    };

    const requiredFields = [
      { value: materialName, label: t("material.materialName") },
      { value: maxLength, label: t("material.maxLen") },
      { value: thickness, label: t("material.thickness") },
      { value: shapeType, label: t("material.curObj") },
      { value: price, label: t("material.matetialPrice") },
    ];

    const missingFields = [];
    requiredFields.forEach(field => {
      if (typeof field.value === 'string' && field.value.trim() === '') {
        missingFields.push(field.label);
      } else if (field.value === null || field.value === undefined) {
        missingFields.push(field.label);
      }
    });

    if (missingFields.length > 0) {
      const errorMessage = `${t("action.saveError")}: ${t("validation.missingFields")}: ${missingFields.join(', ')}.`;
      setSaveError(errorMessage);
      Swal.fire({
        icon: 'error',
        title: t("validation.validationError"),
        text: errorMessage,
      });
      setIsSaving(false);
      return;
    }

    try {
      // Change the URL to /api/edit/material and send ID in the body
      const response = await fetch(`/api/edit/material`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(materialData),
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("text/html")) {
          const errorText = await response.text();
          console.error("Backend returned HTML error:", errorText);
          throw new Error(`Server error (${response.status}): Please check backend logs. Response was HTML.`);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
      }

      setSaveSuccess(true);
      Swal.fire({
        icon: 'success',
        title: 'Save Successfully!',
        showConfirmButton: true,
        confirmButtonText: 'OK'
      }).then(() => {
        // After successful save, re-fetch all materials to update the table
        fetchAllMaterials();
        // Navigate to /material on successful save
        navigate("/material");
      });

    } catch (err) {
      const displayErrorMessage = err.message;
      setSaveError(displayErrorMessage);
      console.error("Error saving material:", err);
      Swal.fire({
        icon: 'error',
        title: t("action.saveError"),
        text: displayErrorMessage,
      });
      // Stay on the current page if save fails (no navigate call here)
    } finally {
      setIsSaving(false);
    }
  };

  const handleTableEditClick = (material) => {
    // When a row's "Edit" button is clicked, populate the form fields with that material's data
    setCurrentMaterialId(material.id);
    setMaterialName(material.material_name || "");
    setMaxLength(material.maximum_length || "");
    setThickness(material.thickness || "");
    setShapeType(material.shape_type || "");
    setPrice(material.price || "");
    setFetchError(null); // Clear any previous fetch errors
    setIsLoading(false); // No longer loading the specific item as it's from table
    // Removed the navigate call to prevent URL change and ensure data populates without "going to another page"
  };

  const handleTableDeleteClick = async (materialIdToDelete) => {
    // Check if the material to be deleted is currently being edited
    if (currentMaterialId === materialIdToDelete) {
      Swal.fire({
        icon: 'error',
        title: 'Cannot Delete Material',
        text: 'You cannot delete the material that is currently being edited. Please select another material or clear the form first.',
      });
      return; // Stop the deletion process
    }

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await fetch("/api/delete/material", {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ id: materialIdToDelete }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
          }

          Swal.fire(
            'Deleted!',
            'Your material has been deleted.',
            'success'
          );
          // Re-fetch materials to update the list after deletion
          fetchAllMaterials();
          // If the deleted material was the one being edited, clear the form and URL
          if (currentMaterialId === materialIdToDelete) {
            setCurrentMaterialId(null);
            setMaterialName("");
            setMaxLength("");
            setThickness("");
            setShapeType("");
            setPrice("");
            // No navigation here either, just clear the form
          }
        } catch (err) {
          console.error("Error deleting material:", err);
          Swal.fire(
            'Error!',
            `Failed to delete material: ${err.message}`,
            'error'
          );
        }
      }
    });
  };

  // Pagination logic for the table
  const paginatedData = tableData.slice(currentPage * 10, (currentPage + 1) * 10);
  const totalPages = Math.ceil(tableData.length / 10);

  return (
    <div> {/* This outer div now allows the content inside to take full width */}
      {/* Header and Buttons Section */}
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1
          data-testid="text-editFormMaterial-edit"
          name="texteditFormMaterialedit"
          className="text-2xl font-bold mb-6"
        >
          {t("material.materialEdit")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          {/* Using custom Savebutton component */}
          <Savebutton
            dataTestId="button-editFormMaterial-save"
            name="buttonEditFormMaterialSave"
            onClick={handleSave}
            disabled={isSaving || isLoading}
          />
          {/* Using custom Canclebutton component */}
          <Canclebutton
            dataTestId="button-editFormMaterial-cancel"
            name="buttonEditFormMaterialCancel"
            onClick={handleCancel}
            disabled={isSaving || isLoading}
          />
        </div>
      </div>

      {/* Form Section */}
      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-editFormMaterial-name"
                name="textEditFormMaterialname"
                className="block font-bold mb-1"
              >
                {t("material.materialName")}
              </label>
              <input
                data-testid="input-editFormMaterial-name"
                name="inputEditFormMaterialname"
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={materialName}
                onChange={(e) => setMaterialName(e.target.value)}
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-editFormMaterial-shape"
                name="textEditFormMaterialshape"
                className="block font-bold mb-1"
              >
                {t("material.curObj")}
              </label>
              <select
                data-testid="select-editFormMaterial-shape"
                name="selectEditFormMaterialshape"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={shapeType}
                onChange={(e) => setShapeType(e.target.value)}
              >
                <option value="">Select a shape</option>
                <option value="Round">Round</option>
                <option value="Square">Square</option>
                <option value="Rectangle">Rectangle</option>
              </select>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-editFormMaterial-length"
                name="textEditFormMateriallength"
                className="block font-bold mb-1"
              >
                {t("material.maxLen")}
              </label>
              <input
                data-testid="input-editFormMaterial-length"
                name="inputEditFormMateriallength"
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={maxLength}
                onChange={(e) => setMaxLength(e.target.value)}
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-editFormMaterial-price"
                name="textEditFormMaterialprice"
                className="block font-bold mb-1"
              >
                {t("material.matetialPrice")}
              </label>
              <input
                data-testid="input-editFormMaterial-price"
                name="inputEditFormMaterialprice"
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
              />
            </div>
          </div>

          <div className="flex flex-col items-start gap-2">
            <label
              data-testid="text-editFormMaterial-thicknesses"
              name="textEditFormMaterialthicknesses"
              className="block font-bold "
            >
              {t("material.thickness")}
            </label>
            <input
              data-testid="input-editFormMaterial-thicknesses"
              name="inputEditFormMaterialthicknesses"
              type="number"
              className="w-full md:w-96 border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              value={thickness}
              onChange={(e) => setThickness(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Status Messages */}
      {isSaving && (
        <div className="text-center py-2 text-blue-600">
          {t("action.saving")}...
        </div>
      )}
      {saveError && (
        <div className="text-center py-2 text-red-600">
          {saveError}
        </div>
      )}
      {saveSuccess && (
        <div className="text-center py-2 text-green-600">
          {t("action.saveSuccess")}!
        </div>
      )}

      {/* --- Table Section with Scrollability --- */}
      <div className="overflow-x-auto mt-3">
        <div className="max-h-80 overflow-y-auto border border-gray-300 rounded-md shadow-md">
          <table
            data-testid="table-materialsList"
            name="tableMaterialsList"
            className="w-full bg-white border-collapse whitespace-nowrap text-sm"
          >
            <thead className="bg-[#4472C4] text-white sticky top-0 z-10">
              <tr>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.id")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.materialName")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.maxLen")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.thickness")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.curObj")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.matetialPrice")}
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  {t("material.operation")}
                </th>
              </tr>
            </thead>
            <tbody>
              {loadingTableData ? (
                <tr>
                  <td colSpan="7" className="text-center py-4 text-gray-500">
                    {t("material.loadingData")}
                  </td>
                </tr>
              ) : tableDataError ? (
                <tr>
                  <td colSpan="7" className="text-center py-4 text-red-600">
                    {t("material.errorLoadingData")}: {tableDataError.message}
                  </td>
                </tr>
              ) : paginatedData.length > 0 ? (
                paginatedData.map((item, index) => (
                  <tr
                    key={item.id || index}
                    className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
                  >
                    <td className="border border-gray-300 px-4 py-1">
                      {item.id}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.material_name}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.maximum_length}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.thickness}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.shape_type}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      {item.price}
                    </td>
                    <td className="border border-gray-300 px-4 py-1">
                      <div className="flex justify-center space-x-2">
                        <Editbutton
                          dataTestId={`button-table-edit-${item.id}`}
                          name={`buttonTableEdit${item.id}`}
                          onClick={() => handleTableEditClick(item)} // This populates the form fields
                        />
                        <Deletebutton
                          dataTestId={`button-table-delete-${item.id}`}
                          name={`buttonTableDelete${item.id}`}
                          onClick={() => handleTableDeleteClick(item.id)}
                        />
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="text-center py-4 text-gray-500">
                    {t("material.noDataFound")}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      {/* --- End Table Section --- */}

      {/* Footer for pagination */}
      <Footer
        className="fixed table-footer-group"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={tableData.length}
      />
    </div>
  );
}

export default MaterialsEdit;
